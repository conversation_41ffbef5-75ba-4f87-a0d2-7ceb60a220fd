/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AcupointAdviceContent: typeof import('./../views/platform-resource/servicePackageManagement/components/AcupointAdviceContent.vue')['default']
    AddGoals: typeof import('./../views/data-statistics/operationalGoals/components/AddGoals.vue')['default']
    AdviceContent: typeof import('./../views/platform-resource/adviceManagement/components/AdviceContent.vue')['default']
    AppLink: typeof import('./../components/AppLink/index.vue')['default']
    AppMain: typeof import('./../layout/components/AppMain/index.vue')['default']
    Base: typeof import('./../views/platform-resource/adviceManagement/components/Base.vue')['default']
    BaseCertification: typeof import('./../views/user-management/patientCertificationReview/components/BaseCertification.vue')['default']
    BaseChat: typeof import('./../components/BaseChat/index.vue')['default']
    BaseInfo: typeof import('./../views/user-management/doctorQuery/components/BaseInfo.vue')['default']
    BaseTableSearchContainer: typeof import('./../components/BaseTableSearchContainer/index.vue')['default']
    BasicInfo: typeof import('./../views/user-management/patientInquiry/components/BasicInfo.vue')['default']
    BodyPartForm: typeof import('./../views/base-data/bodyPartManagement/components/BodyPartForm.vue')['default']
    Breadcrumb: typeof import('./../components/Breadcrumb/index.vue')['default']
    Business: typeof import('./../views/user-management/doctorQuery/components/Business.vue')['default']
    CertificationBasicInfo: typeof import('./../views/user-management/doctorCertificationReview/components/CertificationBasicInfo.vue')['default']
    CertificationContent: typeof import('./../views/user-management/userManagement/components/CertificationContent.vue')['default']
    CertificationForm: typeof import('./../views/user-management/doctorCertificationReview/components/CertificationForm.vue')['default']
    CertificationReviewForm: typeof import('./../views/user-management/doctorCertificationReview/components/CertificationReviewForm.vue')['default']
    CertificationTabs: typeof import('./../views/user-management/doctorCertificationReview/components/CertificationTabs.vue')['default']
    CheckInformation: typeof import('./../views/medical-procedure/rehabilitationPlanInquiry/components/CheckInformation.vue')['default']
    CityPicker: typeof import('./../components/CityPicker/index.vue')['default']
    ConfirmReturn: typeof import('./../views/order-management/depositManagement/components/ConfirmReturn.vue')['default']
    ConsortiumForm: typeof import('./../views/user-management/medicalConsortium/components/ConsortiumForm.vue')['default']
    ConsumableContent: typeof import('./../views/base-data/consumablesManagement/components/ConsumableContent.vue')['default']
    ConsumablesAdviceContent: typeof import('./../views/platform-resource/servicePackageManagement/components/ConsumablesAdviceContent.vue')['default']
    ConsumableTypeContent: typeof import('./../views/base-data/consumablesManagement/components/ConsumableTypeContent.vue')['default']
    CopyButton: typeof import('./../components/CopyButton/index.vue')['default']
    Cost: typeof import('./../views/platform-resource/adviceManagement/components/Cost.vue')['default']
    CreateOrder: typeof import('./../views/order-management/therapyOrder/components/CreateOrder.vue')['default']
    Current: typeof import('./../views/user-management/doctorQuery/components/Current.vue')['default']
    DepositRefund: typeof import('./../views/order-management/depositManagement/components/DepositRefund.vue')['default']
    DeptSelect: typeof import('./../components/DeptSelect/index.vue')['default']
    DetailContent: typeof import('./../views/order-management/depositManagement/components/DetailContent.vue')['default']
    DeviceReturn: typeof import('./../views/order-management/depositManagement/components/DeviceReturn.vue')['default']
    DictLabel: typeof import('./../components/Dict/DictLabel.vue')['default']
    DiseaseContent: typeof import('./../views/base-data/diseaseManagement/components/DiseaseContent.vue')['default']
    DiseaseTypeContent: typeof import('./../views/base-data/diseaseManagement/components/DiseaseTypeContent.vue')['default']
    DoctorContent: typeof import('./../views/user-management/doctorQuery/components/DoctorContent.vue')['default']
    DoctorSettlementDetails: typeof import('./../views/data-statistics/doctorSettlementStatistics/components/DoctorSettlementDetails.vue')['default']
    DownloadTask: typeof import('./../layout/components/NavBar/components/DownloadTask.vue')['default']
    DragContainer: typeof import('./../components/DragContainer/index.vue')['default']
    DysfunctionContent: typeof import('./../views/base-data/dysfunctionManagement/components/DysfunctionContent.vue')['default']
    DysfunctionTypeContent: typeof import('./../views/base-data/dysfunctionManagement/components/DysfunctionTypeContent.vue')['default']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBacktop: typeof import('element-plus/es')['ElBacktop']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElImageViewer: typeof import('element-plus/es')['ElImageViewer']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElInputTag: typeof import('element-plus/es')['ElInputTag']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSelectV2: typeof import('element-plus/es')['ElSelectV2']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTransfer: typeof import('element-plus/es')['ElTransfer']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    ExecutionForm: typeof import('./../views/medical-procedure/followUpManagement/components/ExecutionForm.vue')['default']
    FileUpload: typeof import('./../components/Upload/FileUpload.vue')['default']
    FollowUpDetail: typeof import('./../views/medical-procedure/followUpResult/components/FollowUpDetail.vue')['default']
    FollowUpForm: typeof import('./../views/medical-procedure/followUpManagement/components/FollowUpForm.vue')['default']
    FormItem: typeof import('./../components/FormItem/index.vue')['default']
    FormItemContainer: typeof import('./../components/FormItemContainer/index.vue')['default']
    Fullscreen: typeof import('./../components/Fullscreen/index.vue')['default']
    GaugeForm: typeof import('./../views/platform-resource/gaugeManagement/components/GaugeForm.vue')['default']
    GaugeListDetails: typeof import('./../views/medical-procedure/rehabilitationPlanInquiry/components/GaugeListDetails.vue')['default']
    GaugeQuestion: typeof import('./../views/platform-resource/gaugeManagement/components/GaugeQuestion.vue')['default']
    GeneralAction: typeof import('./../views/platform-resource/servicePackageManagement/components/GeneralAction.vue')['default']
    GeneralAdviceContent: typeof import('./../views/platform-resource/servicePackageManagement/components/GeneralAdviceContent.vue')['default']
    GoalDetails: typeof import('./../views/data-statistics/operationalGoals/components/GoalDetails.vue')['default']
    GroupDetails: typeof import('./../views/data-statistics/operationalGoals/components/GroupDetails.vue')['default']
    Hamburger: typeof import('./../components/Hamburger/index.vue')['default']
    HospitalBaseInfo: typeof import('./../views/user-management/hospitalManagement/components/HospitalBaseInfo.vue')['default']
    HospitalContent: typeof import('./../views/user-management/hospitalManagement/components/HospitalContent.vue')['default']
    HospitalOpenPrescription: typeof import('./../views/user-management/hospitalManagement/components/HospitalOpenPrescription.vue')['default']
    HospitalQualification: typeof import('./../views/user-management/hospitalManagement/components/HospitalQualification.vue')['default']
    HospitalSelect: typeof import('./../components/HospitalSelect/index.vue')['default']
    HospitalTransfer: typeof import('./../components/HospitalTransfer/index.vue')['default']
    IcdContent: typeof import('./../views/base-data/icd10Management/components/IcdContent.vue')['default']
    IconSelect: typeof import('./../components/IconSelect/index.vue')['default']
    IdentityContent: typeof import('./../components/IdentityContent/index.vue')['default']
    KSelect: typeof import('./../components/KSelect/index.vue')['default']
    LangSelect: typeof import('./../components/LangSelect/index.vue')['default']
    LayoutSelect: typeof import('./../layout/components/Settings/components/LayoutSelect.vue')['default']
    LockScreen: typeof import('./../components/LockScreen/index.vue')['default']
    LogisticsInfo: typeof import('./../components/LogisticsInfo/index.vue')['default']
    MedicalRecordDetail: typeof import('./../views/user-management/patientInquiry/components/MedicalRecordDetail.vue')['default']
    MedicalRecordInfo: typeof import('./../views/medical-procedure/medicalRecordInquiry/components/MedicalRecordInfo.vue')['default']
    MedicalRecordList: typeof import('./../views/user-management/patientInquiry/components/MedicalRecordList.vue')['default']
    MedicalRecordTabs: typeof import('./../views/medical-procedure/medicalRecordInquiry/components/MedicalRecordTabs.vue')['default']
    MenuSearch: typeof import('./../components/MenuSearch/index.vue')['default']
    MessageCustom: typeof import('./../components/BaseChat/components/MessageCustom.vue')['default']
    MessageImage: typeof import('./../components/BaseChat/components/MessageImage.vue')['default']
    MessageList: typeof import('./../components/BaseChat/components/MessageList.vue')['default']
    MessageSystemText: typeof import('./../components/BaseChat/components/MessageSystemText.vue')['default']
    MessageText: typeof import('./../components/BaseChat/components/MessageText.vue')['default']
    MessageVideo: typeof import('./../components/BaseChat/components/MessageVideo.vue')['default']
    MissionaryAdviceContent: typeof import('./../views/platform-resource/servicePackageManagement/components/MissionaryAdviceContent.vue')['default']
    MultiImageUpload: typeof import('./../components/Upload/MultiImageUpload.vue')['default']
    MultipleCityPicker: typeof import('./../components/MultipleCityPicker/index.vue')['default']
    MyNotice: typeof import('./../views/system/notice/components/MyNotice.vue')['default']
    NavBar: typeof import('./../layout/components/NavBar/index.vue')['default']
    NavbarRight: typeof import('./../layout/components/NavBar/components/NavbarRight.vue')['default']
    NoticeDetail: typeof import('./../views/system/notice/components/NoticeDetail.vue')['default']
    Notification: typeof import('./../layout/components/NavBar/components/Notification.vue')['default']
    OAContent: typeof import('./../views/ai-services/started/components/OAContent.vue')['default']
    OnlineConsultationRecord: typeof import('./../views/medical-procedure/medicalRecordInquiry/components/OnlineConsultationRecord.vue')['default']
    OrganizationConsortiumForm: typeof import('./../views/user-management/medicalConsortium/components/OrganizationConsortiumForm.vue')['default']
    OrganizationContent: typeof import('./../views/user-management/userManagement/components/OrganizationContent.vue')['default']
    Pagination: typeof import('./../components/Pagination/index.vue')['default']
    PatientCertificationContent: typeof import('./../views/user-management/patientCertificationReview/components/PatientCertificationContent.vue')['default']
    PatientSelect: typeof import('./../views/medical-procedure/followUpManagement/components/PatientSelect.vue')['default']
    PatientSymptoms: typeof import('./../views/medical-procedure/medicalRecordInquiry/components/PatientSymptoms.vue')['default']
    PatientTabs: typeof import('./../views/user-management/patientInquiry/components/PatientTabs.vue')['default']
    PhraseForm: typeof import('./../views/base-data/commonPhraseManagement/components/PhraseForm.vue')['default']
    PlanDetails: typeof import('./../views/medical-procedure/rehabilitationPlanInquiry/components/PlanDetails.vue')['default']
    ProcedureGaugeDetail: typeof import('./../views/medical-procedure/rehabilitationPlanInquiry/components/ProcedureGaugeDetail.vue')['default']
    ProcedureMedicalInfo: typeof import('./../views/medical-procedure/rehabilitationPlanInquiry/components/ProcedureMedicalInfo.vue')['default']
    ProfessionalTitleContent: typeof import('./../views/base-data/professionalTitleManagement/components/ProfessionalTitleContent.vue')['default']
    QRCode: typeof import('./../components/QRCode/index.vue')['default']
    RecoveryMissionForm: typeof import('./../views/platform-resource/recoveryMissionManagement/components/RecoveryMissionForm.vue')['default']
    RefundContent: typeof import('./../views/order-management/refundManagement/components/RefundContent.vue')['default']
    RehabilitationTraining: typeof import('./../views/platform-resource/adviceManagement/components/RehabilitationTraining.vue')['default']
    RemarkForm: typeof import('./../views/data-statistics/doctorStatusStatistics/components/RemarkForm.vue')['default']
    ResearchGaugeForm: typeof import('./../views/research-gauge/research-gauge/components/ResearchGaugeForm.vue')['default']
    ResearchGaugeQuestion: typeof import('./../views/research-gauge/research-gauge/components/ResearchGaugeQuestion.vue')['default']
    RoleForm: typeof import('./../views/user-management/roleManagement/components/RoleForm.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScaleAdviceContent: typeof import('./../views/platform-resource/servicePackageManagement/components/ScaleAdviceContent.vue')['default']
    SchemeAcupointAdviceContent: typeof import('./../views/common-scheme/commonSchemeManagement/components/SchemeAcupointAdviceContent.vue')['default']
    SchemeConsumablesAdviceContent: typeof import('./../views/common-scheme/commonSchemeManagement/components/SchemeConsumablesAdviceContent.vue')['default']
    SchemeGeneralAction: typeof import('./../views/common-scheme/commonSchemeManagement/components/SchemeGeneralAction.vue')['default']
    SchemeGeneralAdviceContent: typeof import('./../views/common-scheme/commonSchemeManagement/components/SchemeGeneralAdviceContent.vue')['default']
    SchemeMissionaryAdviceContent: typeof import('./../views/common-scheme/commonSchemeManagement/components/SchemeMissionaryAdviceContent.vue')['default']
    SchemeScaleAdviceContent: typeof import('./../views/common-scheme/commonSchemeManagement/components/SchemeScaleAdviceContent.vue')['default']
    SendExpress: typeof import('./../views/order-management/therapyOrder/components/SendExpress.vue')['default']
    SendGoodsForm: typeof import('./../views/order-management/tableCardOrder/components/SendGoodsForm.vue')['default']
    ServiceContent: typeof import('./../views/platform-resource/servicePackageManagement/components/ServiceContent.vue')['default']
    SetPriceContent: typeof import('./../views/platform-resource/servicePackageManagement/components/SetPriceContent.vue')['default']
    Settings: typeof import('./../layout/components/Settings/index.vue')['default']
    SetUserRolesContent: typeof import('./../views/user-management/userManagement/components/SetUserRolesContent.vue')['default']
    Sidebar: typeof import('./../layout/components/Sidebar/index.vue')['default']
    SidebarLogo: typeof import('./../layout/components/Sidebar/components/SidebarLogo.vue')['default']
    SidebarMenu: typeof import('./../layout/components/Sidebar/components/SidebarMenu.vue')['default']
    SidebarMenuItem: typeof import('./../layout/components/Sidebar/components/SidebarMenuItem.vue')['default']
    SidebarMenuItemTitle: typeof import('./../layout/components/Sidebar/components/SidebarMenuItemTitle.vue')['default']
    SidebarMixTopMenu: typeof import('./../layout/components/Sidebar/components/SidebarMixTopMenu.vue')['default']
    SingleFileUpload: typeof import('./../components/Upload/SingleFileUpload.vue')['default']
    SingleImageUpload: typeof import('./../components/Upload/SingleImageUpload.vue')['default']
    SizeSelect: typeof import('./../components/SizeSelect/index.vue')['default']
    TableSelect: typeof import('./../components/TableSelect/index.vue')['default']
    TagsSelect: typeof import('./../components/TagsSelect/index.vue')['default']
    TagsView: typeof import('./../layout/components/TagsView/index.vue')['default']
    TBSearchContainer: typeof import('./../components/TBSearchContainer/index.vue')['default']
    ThemeColorPicker: typeof import('./../layout/components/Settings/components/ThemeColorPicker.vue')['default']
    TrainingProgramme: typeof import('./../views/medical-procedure/rehabilitationPlanInquiry/components/TrainingProgramme.vue')['default']
    TreatmentPlanInfo: typeof import('./../views/medical-procedure/medicalRecordInquiry/components/TreatmentPlanInfo.vue')['default']
    TreatmentPoint: typeof import('./../views/platform-resource/adviceManagement/components/TreatmentPoint.vue')['default']
    TreatmentSchemeContent: typeof import('./../views/common-scheme/commonSchemeManagement/components/TreatmentSchemeContent.vue')['default']
    TreatOrderDetailInfo: typeof import('./../views/order-management/therapyOrder/components/TreatOrderDetailInfo.vue')['default']
    UpdateAddress: typeof import('./../views/order-management/therapyOrder/components/UpdateAddress.vue')['default']
    UserBase: typeof import('./../views/user-management/userManagement/components/UserBase.vue')['default']
    UserCertificate: typeof import('./../views/user-management/doctorQuery/components/UserCertificate.vue')['default']
    UserContent: typeof import('./../views/user-management/userManagement/components/UserContent.vue')['default']
    UserProfile: typeof import('./../layout/components/NavBar/components/UserProfile.vue')['default']
    UserSelect: typeof import('./../components/UserSelect/index.vue')['default']
    UserSettlement: typeof import('./../views/user-management/doctorQuery/components/UserSettlement.vue')['default']
    VerifySlider: typeof import('./../components/verify-slider/index.vue')['default']
    VScaleScreen: typeof import('./../components/VScaleScreen.vue')['default']
    WaitEffectiveAction: typeof import('./../views/medical-procedure/rehabilitationPlanInquiry/components/WaitEffectiveAction.vue')['default']
    WangEditor: typeof import('./../components/WangEditor/index.vue')['default']
    WorkerTypeContent: typeof import('./../views/base-data/workerTypeManagement/components/WorkerTypeContent.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
