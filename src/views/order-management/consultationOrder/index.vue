<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间类型">
                <KSelect
                  v-model="queryParams.timeType"
                  :data="[
                    { label: '创建时间', value: '0' },
                    { label: '支付时间', value: '1' },
                    { label: '接诊时间', value: '2' },
                  ]"
                />
              </el-form-item>
              <el-form-item label="时间">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  unlink-panels
                  style="width: 250px"
                  :default-value="[dayjs().startOf('month').toDate(), dayjs().toDate()]"
                  :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                />
              </el-form-item>
              <el-form-item label="订单状态">
                <KSelect
                  v-model="queryParams.stateList"
                  multiple
                  clearable
                  collapse-tags
                  :data="[
                    {
                      label: '待支付',
                      value: '0',
                    },
                    {
                      label: '待接诊',
                      value: '1',
                    },
                    {
                      label: '咨询中',
                      value: '2',
                    },
                    {
                      label: '已完成',
                      value: '3',
                    },
                    {
                      label: '已取消',
                      value: '4',
                    },
                    {
                      label: '未知',
                      value: '5',
                    },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="医院">
                <HospitalSelect
                  v-model="queryParams.orgIdList"
                  multiple
                  collapse-tags
                  filterable
                  @change="
                    () => {
                      queryParams.consultWayList = [];
                      queryParams.doctorUserIdList = [];
                      queryParams.deptIdList = [];
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect
                  v-model="queryParams.deptIdList"
                  :org-id="queryParams.orgIdList.length > 0 ? queryParams.orgIdList[0] : undefined"
                  :disabled="queryParams.orgIdList.length !== 1"
                  filterable
                  multiple
                  collapse-tags
                />
              </el-form-item>
              <el-form-item label="服务类型">
                <KSelect
                  v-model="queryParams.consultWayList"
                  multiple
                  collapse-tags
                  :data="[
                    {
                      label: '问诊咨询',
                      value: '1',
                    },
                    {
                      label: '治疗咨询',
                      value: '2',
                    },
                    {
                      label: '护士咨询',
                      value: '3',
                    },
                  ]"
                  :show-all="true"
                  @change="() => (queryParams.doctorUserIdList = [])"
                />
              </el-form-item>
              <el-form-item label="接诊人">
                <UserSelect
                  v-model="queryParams.doctorUserIdList"
                  :disabled="!queryParams.consultWayList.length"
                  :org-ids="queryParams.orgIdList.length > 0 ? queryParams.orgIdList : null"
                  :dept-ids="queryParams.deptIdList.length > 0 ? queryParams.deptIdList : null"
                  :role-types="roleTypes"
                  :scopeable="false"
                  filterable
                  multiple
                  collapse-tags
                />
              </el-form-item>
              <el-form-item label="是否测试数据">
                <KSelect
                  v-model="queryParams.isTest"
                  :data="[
                    { label: '是', value: '是' },
                    { label: '否', value: '否' },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="关键字">
                <el-input
                  v-model="queryParams.keywords"
                  placeholder="订单编号/患者姓名"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="onExport">导出</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="OrderNo"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
        >
          <el-table-column prop="OrderNo" label="订单编号" min-width="150" show-overflow-tooltip />
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            width="140"
            :formatter="
              (row, column, cellValue, index) =>
                tableDateFormat(row, column, cellValue, index, 'YYYY-MM-DD HH:mm')
            "
          />
          <el-table-column
            prop="PayTime"
            label="支付时间"
            width="140"
            :formatter="
              (row, column, cellValue, index) =>
                tableDateFormat(row, column, cellValue, index, 'YYYY-MM-DD HH:mm')
            "
          />
          <el-table-column
            prop="VistDate"
            label="接诊时间"
            width="140"
            :formatter="
              (row, column, cellValue, index) =>
                tableDateFormat(row, column, cellValue, index, 'YYYY-MM-DD HH:mm')
            "
          />
          <el-table-column
            prop="WeChatTradeNo"
            label="微信支付业务单号"
            width="140"
            show-overflow-tooltip
          />
          <el-table-column
            label="患者信息"
            prop="PatientInfo"
            show-overflow-tooltip
            min-width="120"
          />
          <el-table-column label="服务类型" prop="ConsultWay" show-overflow-tooltip width="100" />
          <el-table-column prop="OrganizationName" label="医院" width="140" />
          <el-table-column prop="DoctorName" label="接诊人" width="80" />
          <el-table-column prop="Amount" label="订单金额" width="80" />
          <el-table-column prop="AssistantName" label="医助" width="80" />
          <el-table-column prop="State" label="状态" width="80" />
          <el-table-column prop="IsTest" label="是否测试数据" width="80" />
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { ConsultationOrderParams, ExportTaskRedashDTO } from "@/api/report/types";
import Report_Api from "@/api/report";
import { convertToRedashParams, exportExcel, getExportCols } from "@/utils/serviceUtils";
import { ExportEnum } from "@/enums/Other";

interface QueryParams extends RedashParameters<ConsultationOrderParams> {
  consultWayList: string[];
  deptIdList: string[];
  doctorUserIdList: string[];
  orgIdList: string[];
  stateList: string[];
}

// 调试开关
const kEnableDebug = false;
defineOptions({
  name: "ConsultationOrder",
});

const {
  kTableRef,
  tableRef,
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  tableDateFormat,
  exportLoading,
} = useTableConfig<ConsultationOrder>();
let queryResultId = -1;

// 查询条件
const queryParams = reactive<QueryParams>({
  consultWays: undefined,
  consultWayList: [],
  deptIds: undefined,
  deptIdList: [],
  doctorUserIds: undefined,
  doctorUserIdList: [],
  endTimeDt: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  isTest: "否",
  keywords: undefined,
  orgIds: undefined,
  orgIdList: [],
  startTimeDt: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
  states: undefined,
  stateList: [],
  timeType: "0",
  pageIndex: 1,
  pageSize: 10,
});

// 定义 dateRange
const dateRange = computed({
  get() {
    // 从 queryParams 中获取日期范围
    return [queryParams.startTimeDt, queryParams.endTimeDt];
  },
  set(newValue) {
    // 当用户选择日期范围时，更新 queryParams
    if (newValue && newValue.length === 2) {
      queryParams.startTimeDt = newValue[0].split(" ")[0] + " 00:00:00";
      queryParams.endTimeDt = newValue[1].split(" ")[0] + " 23:59:59";
    }
  },
});

// 计算角色类型
const roleTypes = computed(() => {
  const types = ["doctor", "therapist", "nurse"];
  if (queryParams.consultWayList.length === 0) {
    return types;
  }

  return queryParams.consultWayList.map((way) => {
    return types[Number(way) - 1];
  });
});

watch(
  () => queryParams.consultWayList,
  () => {
    if (queryParams.consultWayList.length === 0) {
      queryParams.consultWays = undefined;
    } else {
      queryParams.consultWays = queryParams.consultWayList.join(",");
    }
  }
);

watch(
  () => queryParams.deptIdList,
  () => {
    if (queryParams.deptIdList.length === 0) {
      queryParams.deptIds = undefined;
    } else {
      queryParams.deptIds = queryParams.deptIdList.join(",");
    }
  }
);

watch(
  () => queryParams.doctorUserIdList,
  () => {
    if (queryParams.doctorUserIdList.length === 0) {
      queryParams.doctorUserIds = undefined;
    } else {
      queryParams.doctorUserIds = queryParams.doctorUserIdList.join(",");
    }
  }
);

watch(
  () => queryParams.stateList,
  () => {
    if (queryParams.stateList.length === 0) {
      queryParams.states = undefined;
    } else {
      queryParams.states = queryParams.stateList.join(",");
    }
  }
);

watch(
  () => queryParams.orgIdList,
  () => {
    if (queryParams.orgIdList.length === 0) {
      queryParams.orgIds = undefined;
    } else {
      queryParams.orgIds = queryParams.orgIdList.join(",");
    }
  }
);

// 点击搜索
function handleQuery() {
  queryParams.pageIndex = 1;
  requestTableList();
}

// 点击导出
async function onExport() {
  kEnableDebug && console.debug("点击导出");

  const { consultWayList, deptIdList, doctorUserIdList, orgIdList, stateList, ...parameters } =
    queryParams;
  const exportParams = convertToRedashParams<RedashParameters<ConsultationOrderParams>>(
    parameters,
    "Report_RegisterConsultOrderSummary"
  );
  const params: ExportTaskRedashDTO = {
    Cols: getExportCols(tableRef.value!.columns as any, "@"),
    ExecutingParams: exportParams.parameters,
    ExportWay: ExportEnum.PlainMySql,
    FileName: `问诊订单-${Date.now()}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.pageIndex,
    PageSize: queryParams.pageIndex,
    QueryName: "Report_RegisterConsultOrderSummary",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const { consultWayList, deptIdList, doctorUserIdList, orgIdList, stateList, ...parameters } =
    queryParams;
  const params = convertToRedashParams<RedashParameters<ConsultationOrderParams>>(
    parameters,
    "Report_RegisterConsultOrderSummary"
  );
  const r = await Report_Api.getRedashList<ConsultationOrder>(params);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  pageData.value = r.Data.Data;
  total.value = r.Data.TotalCount;
  queryResultId = r.Data.QueryResultId;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
