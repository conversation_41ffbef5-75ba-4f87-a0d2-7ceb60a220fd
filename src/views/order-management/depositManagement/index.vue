<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="租赁状态">
                <el-select
                  v-model="queryParams.DeviceBizState"
                  filterable
                  placeholder="请选择租赁状态"
                  clearable
                  :empty-values="['', undefined, null]"
                  :value-on-clear="() => null"
                  style="width: 130px"
                >
                  <el-option
                    v-for="item in deviceBizStateOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="设备类型">
                <el-select
                  v-model="queryParams.InstrumentId"
                  filterable
                  clearable
                  :empty-values="['', undefined, null]"
                  :value-on-clear="() => null"
                  placeholder="请选择设备类型"
                >
                  <el-option
                    v-for="item in instrumentsList"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.InstrumentId"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="订单号/姓名"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column
            prop="DeviceOrderNo"
            label="订单编号"
            width="160"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column prop="DeviceName" label="设备名称" align="center" width="160" />
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            align="center"
            width="180"
            :formatter="tableDateFormat"
          />
          <el-table-column prop="ActualDays" width="100" label="使用天数" align="center" />
          <el-table-column prop="RemainDays" width="100" label="剩余天数" align="center" />
          <el-table-column label="用户信息" align="center">
            <template #default="scope">
              <div v-if="scope.row.PatUser">
                <span class="span">姓名：{{ scope.row.PatUser.Name }}</span>
                <br />
                <span class="span">昵称：{{ scope.row.PatUser.NickName }}</span>
                <br />
                <span class="span">
                  手机：
                  {{ scope.row.PatUser.PhoneNumber }}
                </span>
              </div>
              <span v-else>用户信息已注销</span>
            </template>
          </el-table-column>
          <el-table-column prop="OrderAddresssName" width="100" label="收货人姓名" align="center" />
          <el-table-column label="押金金额" width="100" align="center">
            <template #default="scope">
              <span>{{ onGetDepositAmount(scope.row) || "--" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="120" align="center">
            <template #default="scope">
              {{ onGetDeviceBizState(scope.row.DeviceBizState) }}
            </template>
          </el-table-column>
          <el-table-column prop="StorageOptUserName" label="入库操作人" width="80" align="center" />
          <el-table-column fixed="right" label="操作" width="160" align="center">
            <template #default="scope">
              <el-button
                v-if="scope.row.DeviceBizState === DeviceBizStateEnum.PendingReturn"
                link
                type="primary"
                size="small"
                @click="handleReturnDevice(scope.row)"
              >
                退还设备
              </el-button>
              <el-button link type="primary" size="small" @click="handleSeeDetail(scope.row)">
                查看详情
              </el-button>
              <el-button
                v-if="scope.row.DeviceBizState === DeviceBizStateEnum.Returning"
                link
                type="primary"
                size="small"
                @click="handleSeeLogistics(scope.row)"
              >
                查看物流
              </el-button>
              <el-button
                v-if="scope.row.DeviceBizState === DeviceBizStateEnum.Returning"
                link
                type="primary"
                size="small"
                @click="handleConfirmInbound(scope.row)"
              >
                确认入库
              </el-button>
              <el-button
                v-if="scope.row.DeviceBizState === DeviceBizStateEnum.InUse"
                link
                type="primary"
                size="small"
                @click="handleConfirmReturn(scope.row)"
              >
                确认退还
              </el-button>
              <el-button
                v-if="scope.row.DeviceBizState === DeviceBizStateEnum.Inbound"
                link
                type="primary"
                size="small"
                @click="handleDepositRefund(scope.row)"
              >
                退款
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog v-model="dialogVisible.detail" title="订单详情" width="600px" destroy-on-close>
      <DetailContent ref="detailContentRef" :detail-info="detailInfo" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.detail = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible.depositRefund" title="退款" width="600px" destroy-on-close>
      <DepositRefund ref="depositRefundRef" :detail-info="detailInfo" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.depositRefund = false">取消</el-button>
          <el-button type="primary" :loading="dialogLoading" @click="handleDepositRefundSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible.deviceReturn" title="设备退还" width="600px" destroy-on-close>
      <DeviceReturn ref="deviceReturnRef" :detail-info="detailInfo" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.deviceReturn = false">取消</el-button>
          <el-button type="primary" :loading="dialogLoading" @click="handleDeviceReturnSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 物流信息 -->
    <el-dialog
      v-model="dialogVisible.logistics"
      title="查看物流"
      width="800"
      destroy-on-close
      @close="dialogVisible.logistics = false"
    >
      <LogisticsInfo
        :phone="detailInfo?.PatUser?.PhoneNumber"
        :expresses="detailInfo?.DeviceOrderInfo?.OrderExpresses.filter((s) => s.Type === 1) || []"
        @cancel="dialogVisible.logistics = false"
      />
    </el-dialog>
    <!-- 确认退还 -->
    <el-dialog
      v-model="dialogVisible.confirmReturn"
      title="确认退还"
      width="600px"
      destroy-on-close
    >
      <ConfirmReturn ref="confirmReturnRef" :detail-info="detailInfo" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.confirmReturn = false">取消</el-button>
          <el-button type="primary" :loading="dialogLoading" @click="handleConfirmReturnSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { DeviceManageInputDTO, DeviceManageItem } from "@/api/consult/types";
import { DeviceBizStateEnum } from "@/enums/PrescriptionEnum";
import Content_Api from "@/api/content";
import Consult_Api from "@/api/consult";
import Order_Api from "@/api/order";
import { useUserStore } from "@/store";
import DeviceReturn from "./components/DeviceReturn.vue";
import Supplier_Jiandao_Api from "@/api/supplier-jiandao";

const userStore = useUserStore();

defineOptions({
  name: "DepositManagement",
});

interface DialogVisible {
  detail: boolean;
  depositRefund: boolean;
  deviceReturn: boolean;
  logistics: boolean;
  confirmReturn: boolean;
}

const queryParams = ref<DeviceManageInputDTO>({
  PageIndex: 1,
  PageSize: 10,
  Keyword: "",
  DeviceBizState: null,
  InstrumentId: null,
  BeginTime: dayjs().subtract(2, "months").format("YYYY-MM-DD 00:00:00"),
  EndTime: dayjs().format("YYYY-MM-DD 23:59:59"),
});
const timeRange = ref<[string, string]>([
  dayjs().subtract(2, "months").format("YYYY-MM-DD"),
  dayjs().format("YYYY-MM-DD"),
]);
const dialogVisible = reactive<DialogVisible>({
  detail: false,
  depositRefund: false,
  deviceReturn: false,
  logistics: false,
  confirmReturn: false,
});
const detailInfo = ref<DeviceManageItem | null>(null);
const depositRefundRef = useTemplateRef("depositRefundRef");
const deviceReturnRef = useTemplateRef("deviceReturnRef");
const confirmReturnRef = useTemplateRef("confirmReturnRef");

const dialogLoading = ref<boolean>(false);

const deviceBizStateOptions = ref<{ label: string; value: number }[]>([
  { label: "待付款", value: DeviceBizStateEnum.PendingPayment },
  { label: "已支付-待发货", value: DeviceBizStateEnum.PaidToBeShipped },
  { label: "已支付-待收货", value: DeviceBizStateEnum.PaidToBeReceived },
  { label: "已支付-使用中", value: DeviceBizStateEnum.InUse },
  { label: "待退还", value: DeviceBizStateEnum.PendingReturn },
  { label: "退还中", value: DeviceBizStateEnum.Returning },
  { label: "已入库", value: DeviceBizStateEnum.Inbound },
  { label: "已完成", value: DeviceBizStateEnum.Completed },
]);

const instrumentsList = ref<BaseInstrument[]>([]);

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableDateFormat, tableResize } =
  useTableConfig<DeviceManageItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handlePreviewOrEdit = async (row: BaseOrganization | null, isPreviewState: boolean) => {
  isPreview.value = row ? isPreviewState : false;
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Consult_Api.deviceManage(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};

const onGetDepositAmount = (row: DeviceManageItem): string => {
  const currentDeviceItem = row.DeviceOrderInfo.OrderDetails.find(
    (s) => s.RelationId === row.DeviceId
  );
  if (currentDeviceItem) {
    return `¥${currentDeviceItem.Price * 100}`;
  }
  return "¥0";
};

watch(timeRange, (newVal) => {
  queryParams.value.BeginTime = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndTime = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

const onGetInstrumentsList = async () => {
  const res = await Content_Api.getInstrumentsWithCharge();
  if (res.Type === 200) {
    instrumentsList.value = res.Data;
  }
};

const onGetDeviceBizState = (state: number) => {
  const item = deviceBizStateOptions.value.find((v) => v.value === state);
  if (item) {
    return item.label;
  } else {
    return "未找到对应的状态";
  }
};

const handleConfirmInbound = (row: DeviceManageItem) => {
  ElMessageBox.alert("是否确认入库操作？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    showCancelButton: true,
  }).then(() => {
    let orderDetailId = "";
    if (row.DeviceOrderInfo.OrderDetails && row.DeviceOrderInfo.OrderDetails.length > 0) {
      const itemList = row.DeviceOrderInfo.OrderDetails.filter(
        (item) => item.RelationId === row.DeviceId
      );
      if (itemList && itemList.length > 0) {
        orderDetailId = itemList[0].Id;
      }
    }
    const reqData = {
      OrderNo: row.OrderNo,
      OrderDetailId: orderDetailId,
      OptUserId: userStore.userInfo?.Id,
    };
    Consult_Api.setOrderDeviceBizStorage(reqData).then((res) => {
      if (res.Type === 200) {
        ElNotification({
          title: "成功",
          message: res.Content,
          type: "success",
        });
        handleGetTableList();
      } else {
        ElNotification({
          title: "系统提示",
          message: res.Content,
          type: "warning",
        });
      }
    });
  });
};

const handleSeeDetail = (row: DeviceManageItem) => {
  detailInfo.value = row;
  dialogVisible.detail = true;
};

const handleDepositRefund = (row: DeviceManageItem) => {
  detailInfo.value = row;
  dialogVisible.depositRefund = true;
};

const handleReturnDevice = (row: DeviceManageItem) => {
  detailInfo.value = row;
  dialogVisible.deviceReturn = true;
};
const handleSeeLogistics = (row: DeviceManageItem) => {
  detailInfo.value = row;
  dialogVisible.logistics = true;
};
const handleConfirmReturn = async (row: DeviceManageItem) => {
  if (!row.OrderNo) return;
  const res = await Supplier_Jiandao_Api.checkComplete({ orderNo: row.OrderNo });
  if (res.Type !== 200) {
    ElMessage.error(res.Content);
    return;
  }
  if (!res.Data) {
    ElMessage.error("对应发货单流程未结束，请先完成发货流程");
    return;
  }
  detailInfo.value = row;
  dialogVisible.confirmReturn = true;
};

const handleDepositRefundSubmit = async () => {
  const info = await depositRefundRef.value?.handleSubmit();
  if (!info) return;
  dialogLoading.value = true;
  Order_Api.refundDeposit(info)
    .then((res) => {
      if (res.Type === 200) {
        dialogVisible.depositRefund = false;
        handleGetTableList();
      }
    })
    .catch((err) => {
      ElMessage.error(err.message);
    })
    .finally(() => {
      dialogLoading.value = false;
    });
};
const handleDeviceReturnSubmit = async () => {
  const info = await deviceReturnRef.value?.handleSubmit();
  if (!info) return;
  dialogLoading.value = true;
  Consult_Api.returnByOrderDetail(info)
    .then((res) => {
      if (res.Type === 200) {
        dialogVisible.deviceReturn = false;
        handleGetTableList();
      }
    })
    .catch((err) => {
      ElMessage.error(err.message);
    })
    .finally(() => {
      dialogLoading.value = false;
    });
};

const handleConfirmReturnSubmit = async () => {
  const info = await confirmReturnRef.value?.handleSubmit();
  if (!info) return;
  dialogLoading.value = true;
  Supplier_Jiandao_Api.createRecoveryDeviceForm(info)
    .then((res) => {
      if (res.Type === 200) {
        dialogVisible.confirmReturn = false;
        handleGetTableList();
      }
    })
    .catch((err) => {
      ElMessage.error(err.message);
    })
    .finally(() => {
      dialogLoading.value = false;
    });
};

onMounted(() => {
  onGetInstrumentsList();
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
