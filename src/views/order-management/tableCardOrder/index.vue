<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <!-- 选择时间范围 -->
              <el-form-item label="时间">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  unlink-panels
                  style="width: 250px"
                  :default-value="[dayjs().startOf('month').toDate(), dayjs().toDate()]"
                  :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                />
              </el-form-item>
              <el-form-item label="医院" prop="OrgId">
                <HospitalSelect
                  v-model="queryParams.OrgId"
                  @change="
                    () => {
                      queryParams.DeptId = undefined;
                      queryParams.UserId = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="科室" prop="DeptId">
                <DeptSelect
                  v-model="queryParams.DeptId"
                  :org-id="queryParams.OrgId"
                  :disabled="!queryParams.OrgId"
                />
              </el-form-item>
              <el-form-item label="用户" prop="UserId">
                <UserSelect
                  v-model="queryParams.UserId"
                  :disabled="!queryParams.OrgId"
                  :org-ids="queryParams.OrgId ? [queryParams.OrgId!] : null"
                  :dept-ids="queryParams.DeptId ? [queryParams.DeptId!] : null"
                  :scopeable="false"
                />
              </el-form-item>
              <el-form-item label="状态" prop="OrderState">
                <KSelect
                  v-model="queryParams.OrderState"
                  :data="[
                    { label: '待发货', value: 1 },
                    { label: '已发货', value: 2 },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="OrderNo"
          :height="tableFluidHeight"
          highlight-current-row
          border
        >
          <el-table-column prop="OrderNo" label="订单编号" align="center" width="150" />
          <el-table-column prop="OrgName" label="医院" align="center" width="150" />
          <el-table-column prop="DeptName" label="科室" align="center" width="100" />
          <el-table-column prop="UserName" label="用户" align="center" width="80" />
          <el-table-column prop="AssistantName" label="医助" align="center" width="80" />
          <el-table-column prop="CreatedTime" label="申请时间" align="center" width="150" />
          <el-table-column prop="Images" label="台卡" align="center" width="100">
            <template #default="scope">
              <el-image
                v-for="(item, index) in scope.row.Images"
                class="w-60px h-60px"
                :src="item.Url"
                fit="contain"
                preview-teleported
                :initial-index="index"
                :preview-src-list="scope.row.Images.map((e: TableCardImage) => e.Url)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="ShowState" label="状态" align="center" width="100" />
          <el-table-column prop="OrderAddresss" label="收货信息" align="center" min-width="150">
            <template #default="scope">
              <template v-if="scope.row.OrderAddresss.length">
                <div>{{ scope.row.OrderAddresss[0].Name }}</div>
                <div>{{ scope.row.OrderAddresss[0].Tel }}</div>
                <div>
                  {{
                    scope.row.OrderAddresss[0].ProvinceName +
                    scope.row.OrderAddresss[0].CityName +
                    scope.row.OrderAddresss[0].CountyName +
                    scope.row.OrderAddresss[0].Address
                  }}
                </div>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="OrderExpresses" label="物流信息" align="center" width="120">
            <template #default="scope">
              <div v-if="scope.row.OrderExpresses.length">
                {{ scope.row.OrderExpresses[0].ExpressNum }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="CreatorName" label="申请人" align="center" width="80" />
          <!-- 操作 -->
          <el-table-column fixed="right" label="操作" width="120" align="center">
            <template #default="scope">
              <el-button
                v-if="scope.row.OrderState === 1"
                link
                type="primary"
                @click="onSendDelivery(scope.row)"
              >
                发货
              </el-button>
              <el-button
                v-if="scope.row.OrderState === 2"
                link
                type="primary"
                @click="onCheckLogistics(scope.row)"
              >
                查看物流
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 发货 -->
  <el-dialog
    v-model="showSendGoodsDialog.isShow"
    title="发货"
    width="600"
    destroy-on-close
    @close="showSendGoodsDialog.isShow = false"
  >
    <SendGoodsForm
      :order-no="showSendGoodsDialog.orderNo"
      :data="showSendGoodsDialog.data"
      @cancel="showSendGoodsDialog.isShow = false"
      @submit="onConfirmSubmitSendGoods"
    />
  </el-dialog>

  <!-- 物流信息 -->
  <el-dialog
    v-model="showLogisticsDialog.isShow"
    title="查看物流"
    width="800"
    destroy-on-close
    @close="showLogisticsDialog.isShow = false"
  >
    <LogisticsInfo
      :phone="showLogisticsDialog.phone"
      :expresses="showLogisticsDialog.expresses"
      @cancel="showLogisticsDialog.isShow = false"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import Consult_Api from "@/api/consult";
import { GetCardOrdersListParams } from "@/api/consult/types";

// 调试开关
const kEnableDebug = false;
defineOptions({
  name: "TableCardOrder",
});

const stateList = ["待支付", "已支付/待发货", "已发货/待收货", "已完成", "已取消"];
const { tableLoading, tableFluidHeight, pageData, total, tableResize } =
  useTableConfig<ShowTableCardOrder>();

interface ShowTableCardOrder extends TableCardDetail {
  Images?: TableCardImage[];
  ShowState?: string;
}

interface QueryParams extends GetCardOrdersListParams {
  OrderState?: number;
}

// 查询条件
const queryParams = reactive<QueryParams>({
  BeginTime: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
  EndTime: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  PageIndex: 1,
  PageSize: 10,
});

// 定义 dateRange
const dateRange = computed({
  get() {
    // 从 queryParams 中获取日期范围
    return [queryParams.BeginTime, queryParams.EndTime];
  },
  set(newValue) {
    // 当用户选择日期范围时，更新 queryParams
    if (newValue && newValue.length === 2) {
      queryParams.BeginTime = newValue[0].split(" ")[0] + " 00:00:00";
      queryParams.EndTime = newValue[1].split(" ")[0] + " 23:59:59";
    }
  },
});

watch(
  () => queryParams?.OrderState,
  (val) => {
    queryParams.OrderStates = val || val === 0 ? [val] : undefined;
  }
);

// 发货弹框
const showSendGoodsDialog = reactive({
  isShow: false,
  orderNo: "",
  data: [] as OrderExpress[],
});

// 物流信息弹窗
const showLogisticsDialog = reactive({
  isShow: false,
  phone: undefined as string | undefined,
  expresses: [] as OrderExpress[],
});

// 点击搜索
function handleQuery() {
  queryParams.PageIndex = 1;
  requestTableList();
}

// 点击发货
async function onSendDelivery(row: ShowTableCardOrder) {
  kEnableDebug && console.debug("发货", row);
  if (!row.OrderNo) {
    ElMessage.warning("订单编号不存在");
    return;
  }

  showSendGoodsDialog.orderNo = row.OrderNo;
  showSendGoodsDialog.data = row.OrderExpresses ?? [];
  showSendGoodsDialog.isShow = true;
}

// 确认发货
async function onConfirmSubmitSendGoods() {
  kEnableDebug && console.debug("确认发货");

  showSendGoodsDialog.isShow = false;
  showSendGoodsDialog.orderNo = "";
  showSendGoodsDialog.data = [];

  const r = await requestTableList();
  if (r.Type !== 200) {
    return;
  }

  ElNotification.success("发货成功");
}

// 点击查看物流
async function onCheckLogistics(row: ShowTableCardOrder) {
  kEnableDebug && console.debug("查看物流", row);

  if (!row.OrderExpresses?.length) {
    ElMessage.warning("不存在物流信息");
    return;
  }

  showLogisticsDialog.phone = row.OrderAddresss?.[0]?.Tel;
  showLogisticsDialog.expresses = row.OrderExpresses;
  showLogisticsDialog.isShow = true;
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const r = await Consult_Api.getCardOrdersList(queryParams);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return r;
  }

  // 请求成功
  pageData.value = r.Data.Data.map((e) => {
    let images: TableCardImage[] = [];
    try {
      images = JSON.parse(e.CardImgs ?? "[]") ?? [];
    } catch (error) {
      kEnableDebug && console.warn(error);
    }
    return {
      ...e,
      CreatedTime: dayjs(e.CreatedTime).format("YYYY-MM-DD HH:mm:ss"),
      ShowState: e.OrderState || e.OrderState === 0 ? stateList[e.OrderState] : undefined,
      Images: images,
    };
  });
  total.value = r.Data.TotalCount;
  return r;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
