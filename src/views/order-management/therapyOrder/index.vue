<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="订单状态">
                <el-select
                  v-model="queryParams.States"
                  placeholder="请选择"
                  clearable
                  multiple
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  style="width: 120px"
                >
                  <el-option
                    v-for="item in orderState"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="支付方式">
                <el-select
                  v-model="queryParams.PayType"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  style="width: 120px"
                >
                  <el-option
                    v-for="item in payType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="关键字">
                <el-input
                  v-model="queryParams.keyword"
                  placeholder="订单号/收货人/用户姓名/电话/备注"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="地区">
                <CityPicker
                  v-model="areas"
                  style="width: 180px"
                  :is-only-city="true"
                  @change="handleChangeAreas"
                />
              </el-form-item>
              <el-form-item label="医院">
                <HospitalSelect
                  v-if="!areas"
                  v-model="queryParams.OrgIds"
                  placeholder="请选择"
                  multiple
                  style="width: 220px"
                  :scopeable="true"
                  @change="handleChangeOrg"
                />
                <el-select
                  v-else
                  v-model="queryParams.OrgIds"
                  placeholder="请选择"
                  multiple
                  filterable
                  style="width: 220px"
                  @change="handleChangeOrg"
                >
                  <el-option
                    v-for="item in organizationList"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.Id!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect
                  v-model="queryParams.DeptIds"
                  :disabled="!queryParams.OrgIds || queryParams.OrgIds.length !== 1"
                  :org-id="queryParams.OrgIds ? queryParams.OrgIds[0] : null"
                  multiple
                />
              </el-form-item>
              <el-form-item label="下达人">
                <UserSelect
                  v-model="queryParams.DoctorIds"
                  :org-ids="queryParams.OrgIds"
                  :dept-ids="queryParams.DeptIds"
                  :role-types="['doctor', 'therapist', 'nurse']"
                  multiple
                  :scopeable="true"
                />
              </el-form-item>
              <el-form-item label="设备类型">
                <el-select
                  v-model="queryParams.InstrumentIds"
                  placeholder="设备类型"
                  filterable
                  multiple
                  collapse-tags
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  style="width: 150px"
                >
                  <el-option
                    v-for="(item, index) in instrumentList"
                    :key="'prov' + index"
                    :label="item.Name"
                    :value="item.InstrumentId"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="耗材">
                <el-select
                  v-model="queryParams.Consumables"
                  placeholder="请选择"
                  filterable
                  multiple
                  collapse-tags
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  style="width: 150px"
                >
                  <el-option
                    v-for="(item, index) in consumableList"
                    :key="'prov' + index"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="是否测试数据">
                <el-select
                  v-model="queryParams.IsTest"
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  placeholder="请选择"
                  clearable
                  style="width: 100px"
                >
                  <el-option label="是" :value="1" />
                  <el-option label="否" :value="0" />
                </el-select>
              </el-form-item>
              <el-form-item label="金额范围">
                <el-input-number
                  v-model="queryParams.MinTotalAmount"
                  placeholder="最小金额"
                  style="margin-right: 10px"
                  @keyup.enter="handleQuery"
                />
                <el-input-number
                  v-model="queryParams.MaxTotalAmount"
                  placeholder="最大金额"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              v-hasNoPermission="['externalSeller']"
              type="primary"
              @click="handlePreviewOrEdit"
            >
              创建订单
            </el-button>
            <el-button
              v-hasNoPermission="['externalSeller']"
              type="primary"
              :disabled="!pageData.length"
              :loading="exportLoading"
              @click="handleExportExcel"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="TreatOrderNo"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column
            prop="TreatOrderNo"
            label="订单编号"
            width="180"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="InstrumentPara"
            label="设备参数"
            show-overflow-tooltip
            align="center"
            width="180"
          />
          <el-table-column
            prop="ConsumablesName"
            label="耗材明细"
            width="180"
            show-overflow-tooltip
            align="center"
          >
            <template #default="scope">
              {{ handleGetConsumablesName(scope.row.Consumables) }}
            </template>
          </el-table-column>
          <el-table-column prop="DeviceName" label="设备" align="center" width="180">
            <template #default="scope">
              {{ handleGetDeviceName(scope.row.DeviceOrder) }}
            </template>
          </el-table-column>
          <el-table-column prop="PayTypeAlias" label="支付方式" align="center" width="80" />
          <el-table-column prop="PrescriptionIdStr" label="方案编号" width="180" align="center" />
          <el-table-column prop="CityName" label="地区" align="center" width="80" />
          <el-table-column prop="OrganizationName" label="开方医院" align="center" width="120" />
          <el-table-column prop="DptName" label="科室" align="center" width="80" />
          <el-table-column prop="DoctorName" label="下达人" align="center" width="80" />
          <el-table-column prop="AssistantName" label="医助" align="center" width="80" />
          <el-table-column prop="IsTest" label="是否测试数据" align="center" width="80" />
          <el-table-column
            prop="ICDName"
            label="诊断"
            align="center"
            width="180"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{
                scope.row.ICD && scope.row.ICD.map((s: BaseDiagnosis) => s.DiagnoseName).join(",")
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="TherapistRemark"
            label="方案说明"
            align="center"
            width="180"
            show-overflow-tooltip
          />
          <el-table-column prop="CreatedTime" label="创建时间" align="center" width="180" />
          <el-table-column label="用户信息" show-overflow-tooltip width="180" align="center">
            <template #default="scope">
              <div v-if="scope.row.PatUser">
                <span class="span">姓名：{{ scope.row.UserName }}</span>
                <br />
                <span class="span">昵称：{{ scope.row.PatUser.NickName }}</span>
                <br />
                <span class="span">
                  手机：
                  {{ scope.row.PatUser.PhoneNumber }}
                </span>
              </div>
              <span v-else>用户信息已注销</span>
            </template>
          </el-table-column>
          <el-table-column prop="Address" label="收货地址" align="center" width="180">
            <template #default="scope">
              {{ handleGetAddress(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="Gift"
            show-overflow-tooltip
            label="赠品"
            align="center"
            width="180"
          />
          <el-table-column
            prop="GiftMark"
            show-overflow-tooltip
            label="备注"
            align="center"
            width="180"
          />
          <el-table-column label="订单金额" align="center" width="80">
            <template #default="scope">
              <span class="span">¥{{ handleGetTotalAmount(scope.row) }}</span>
              <span v-if="scope.row.DeviceAmount" class="span">
                (含设备押金¥{{ scope.row.DeviceAmount }})
              </span>
            </template>
          </el-table-column>
          <el-table-column label="退款金额" align="center" width="80">
            <template #default="scope">
              <span style="color: red">
                {{ scope.row.TreatRefundAmount * 1 === 0 ? "" : scope.row.TreatRefundAmount * 1 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="物流信息" width="220" show-overflow-tooltip align="center">
            <template #default="scope">
              <div
                v-for="(i, index) in scope.row.TreatOrder.OrderExpresses"
                :key="i.ExpressNum + String(index)"
                style="cursor: pointer"
              >
                <span v-if="i.ExpressNum && i.Type === 0">
                  快递单号：
                  <span style="text-decoration: underline; color: blue">
                    {{ i.ExpressNum }}
                  </span>
                </span>
                <span v-if="i.Remark && i.Type === 0">({{ i.Remark }})</span>
                <br />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="80" align="center">
            <template #default="scope">
              {{ orderState[scope.row.TreatOrder.Status].label }}
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="140" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handleGetOrderDetail(scope.row)">
                查看订单
              </el-button>
              <el-button
                v-if="scope.row.TreatOrder.Status === 1"
                v-hasNoPermission="['externalSeller']"
                link
                type="primary"
                @click="handleSetExpressClick(scope.row, false)"
              >
                发货
              </el-button>
              <el-button
                v-if="scope.row.TreatOrder.Status === 1"
                v-hasNoPermission="['externalSeller']"
                link
                type="primary"
                @click="handleSetAddressClick(scope.row)"
              >
                修改地址
              </el-button>
              <el-button
                v-if="scope.row.TreatOrder.Status === 0"
                v-hasNoPermission="['externalSeller']"
                link
                type="primary"
                @click="handlePayClick(scope.row)"
              >
                支付
              </el-button>
              <el-button
                v-if="scope.row.TreatOrder.Status === 2"
                v-hasNoPermission="['externalSeller']"
                link
                type="primary"
                @click="handleSetExpressClick(scope.row, true)"
              >
                修改快递
              </el-button>
              <el-button
                v-hasNoPermission="['externalSeller']"
                link
                type="primary"
                @click="handleSetOrderRemarkClick(scope.row)"
              >
                备注
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog v-model="dialogVisible.remark" title="备注" width="20%" destroy-on-close>
      <div class="p-10px">
        <div class="flex items-center justify-start m-x-10px">
          <span>赠品：</span>
          <el-input v-model="setOrderRemarkParams.Gift" style="flex: 1" type="textarea" />
        </div>
        <div class="flex items-center justify-start m-x-10px mt-10px">
          <span>备注：</span>
          <el-input v-model="setOrderRemarkParams.Mark" style="flex: 1" type="textarea" />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.remark = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handleSetOrderRemark">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible.express" :title="expressTitle" width="40%" destroy-on-close>
      <SendExpress
        ref="sendExpressRef"
        :express-info="expressInfo"
        :address-info="addressInfo"
        :order-id="orderId"
        :treat-order-no="treatOrderNo"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.express = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handleSetExpress">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible.detail" title="订单详情" width="30%" destroy-on-close>
      <TreatOrderDetailInfo :treat-order-detail="treatOrderDetail" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.detail = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible.updateAddress" title="修改地址" width="40%" destroy-on-close>
      <UpdateAddress
        ref="updateAddressRef"
        :address-info="addressInfo"
        :treat-order-no="treatOrderNo"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.updateAddress = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handleSetAddress">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible.pay" title="支付" width="20%" destroy-on-close>
      <div class="p-10px">
        <div class="flex items-center justify-start m-x-10px">
          <span>备注：</span>
          <el-input v-model="setOrderPayParams.BackendPayRemark" style="flex: 1" type="textarea" />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.pay = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handleSetPay">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible.create" title="创建订单" width="40%" destroy-on-close>
      <CreateOrder ref="createOrderRef" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.create = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handleCreateOrder">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import {
  GetOrderManageInputDTO,
  TherapyDeviceOrder,
  TherapyOrder,
  TreatOrderDetail,
} from "@/api/consult/types";
import { OrderStateEnum, PayTypeEnum } from "@/enums/OrderEnum";
import Consult_Api from "@/api/consult";
import { calculateAmount } from "@/utils";
import { getConsumablesTypeList, getInstrumentTypeList } from "@/utils/dict";
import Passport_Api from "@/api/passport";
import Report_Api from "@/api/report";
import { EventBus } from "@/utils/eventBus";
import { OrderAddressInputDTO } from "@/api/order/types";
import SendExpress from "./components/SendExpress.vue";
import UpdateAddress from "./components/UpdateAddress.vue";
import CreateOrder from "./components/CreateOrder.vue";
import Order_Api from "@/api/order";
import { ExportEnum } from "@/enums/Other";
defineOptions({
  name: "TherapyOrder",
});
const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<TherapyOrder>();

interface DialogVisible {
  remark: boolean;
  express: boolean;
  detail: boolean;
  updateAddress: boolean;
  pay: boolean;
  create: boolean;
}
const queryParams = ref<GetOrderManageInputDTO>({
  PageIndex: 1,
  PageSize: 10,
  BeginTime: dayjs().format("YYYY-MM-01 00:00:00"),
  EndTime: dayjs().format("YYYY-MM-DD 23:59:59"),
  States: null,
  PayType: null,
  keyword: "",
  OrgIds: null,
  DoctorIds: null,
  IsTest: 0,
  InstrumentIds: null,
  Consumables: null,
  MinTotalAmount: null,
  MaxTotalAmount: null,
  DeptIds: null,
  Scopeable: true,
});
const orderState = [
  {
    label: "待支付",
    value: OrderStateEnum.Pending,
  },
  {
    label: "待发货",
    value: OrderStateEnum.Paid,
  },
  {
    label: "待收货",
    value: OrderStateEnum.Shipped,
  },
  {
    label: "已完成",
    value: OrderStateEnum.Completed,
  },
  {
    label: "已取消",
    value: OrderStateEnum.Canceled,
  },
]; // 订单状态 0:待支付 1：已支付/待发货 2：已发货/待收货 3、已完成  已取消
const payType = [
  {
    label: "微信",
    value: PayTypeEnum.WeChat,
  },
  {
    label: "支付宝",
    value: PayTypeEnum.Alipay,
  },
  {
    label: "免费",
    value: PayTypeEnum.Free,
  },
  {
    label: "其他",
    value: PayTypeEnum.Other,
  },
];
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);
const areas = ref<string>("");
const organizationList = ref<BaseOrganization[]>([]);
const instrumentList = ref<BaseInstrument[]>([]);
const consumableList = ref<string[]>([]);
const exportLoading = ref<boolean>(false);
const expressTitle = ref<string>("发货");
const dialogVisible = ref<DialogVisible>({
  remark: false,
  express: false,
  detail: false,
  updateAddress: false,
  pay: false,
  create: false,
});
const dialogConfirmLoading = ref<boolean>(false);
const setOrderRemarkParams = ref<{
  Gift: string;
  Mark: string;
  PrescriptionId: string;
}>({
  Gift: "",
  Mark: "",
  PrescriptionId: "",
});
const setOrderPayParams = ref<{
  OrderNo: string;
  BackendPayRemark: string;
}>({
  OrderNo: "",
  BackendPayRemark: "",
});
const expressInfo = ref<OrderExpress[]>([]);
const addressInfo = ref<OrderAddress[]>([]);
const orderId = ref<string>("");
const treatOrderNo = ref<string>("");
const sendExpressRef = useTemplateRef("sendExpressRef");
const treatOrderDetail = ref<TreatOrderDetail | null>(null);
const updateAddressRef = useTemplateRef("updateAddressRef");
const createOrderRef = useTemplateRef("createOrderRef");
const handlePayClick = (row: TherapyOrder) => {
  setOrderPayParams.value.OrderNo = row.TreatOrderNo;
  setOrderPayParams.value.BackendPayRemark = "";
  dialogVisible.value.pay = true;
};
const handleSetPay = async () => {
  dialogConfirmLoading.value = true;
  Order_Api.backendPayOrder(setOrderPayParams.value)
    .then((res) => {
      if (res.Type === 200) {
        ElNotification.success(res.Content);
        handleGetTableList();
        dialogVisible.value.pay = false;
      } else {
        ElNotification.warning(res.Content);
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};
const handleSetAddress = async () => {
  const params: OrderAddressInputDTO | null = await updateAddressRef.value!.handleSubmit();
  if (!params) return;
  dialogConfirmLoading.value = true;
  Order_Api.setOrderAddress([params])
    .then((res) => {
      if (res.Type === 200) {
        ElNotification.success(res.Content);
        handleGetTableList();
        dialogVisible.value.updateAddress = false;
      } else {
        ElNotification.warning(res.Content);
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};
const handleSetAddressClick = (row: TherapyOrder) => {
  addressInfo.value = row.TreatOrder.OrderAddresss ? row.TreatOrder.OrderAddresss : [];
  treatOrderNo.value = row.TreatOrderNo;
  dialogVisible.value.updateAddress = true;
};

const handleGetOrderDetail = async (row: TherapyOrder) => {
  const res = await Consult_Api.getTreatOrderInfo({ prescriptionId: row.OrderId });
  if (res.Type === 200) {
    treatOrderDetail.value = res.Data;
    dialogVisible.value.detail = true;
  }
};

const handleSetOrderRemarkClick = (row: TherapyOrder) => {
  setOrderRemarkParams.value.PrescriptionId = row.PrescriptionIdStr;
  setOrderRemarkParams.value.Gift = row.Gift || "";
  setOrderRemarkParams.value.Mark = row.GiftMark || "";
  dialogVisible.value.remark = true;
};

const handleSetExpressClick = (row: TherapyOrder, isEdit: boolean) => {
  expressTitle.value = isEdit ? "修改快递" : "发货";
  addressInfo.value = row.TreatOrder.OrderAddresss ? row.TreatOrder.OrderAddresss : [];
  expressInfo.value = isEdit ? row.TreatOrder.OrderExpresses : [];
  orderId.value = row.OrderId;
  treatOrderNo.value = row.TreatOrderNo;
  dialogVisible.value.express = true;
};

const handleSetExpress = async () => {
  const params = await sendExpressRef.value?.handleSubmit();
  if (!params) return;
  dialogConfirmLoading.value = true;
  const fun = expressTitle.value === "修改快递" ? Order_Api.editExpresss : Order_Api.sendExpress;
  fun(params)
    .then((res) => {
      if (res.Type === 200) {
        ElNotification.success(res.Content);
        dialogVisible.value.express = false;
        handleGetTableList();
      } else {
        ElNotification.warning(res.Content);
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

const handleSetOrderRemark = () => {
  if (!setOrderRemarkParams.value.PrescriptionId) {
    ElNotification.warning("当前订单没有方案编号");
    return;
  }
  if (!setOrderRemarkParams.value.Gift && !setOrderRemarkParams.value.Mark) {
    ElNotification.warning("请输入赠品或备注");
    return;
  }
  dialogConfirmLoading.value = true;
  Consult_Api.setOrderGift(setOrderRemarkParams.value)
    .then((res) => {
      if (res.Type === 200) {
        ElNotification.success(res.Content);
        dialogVisible.value.remark = false;
        handleGetTableList();
        setOrderRemarkParams.value.Gift = "";
        setOrderRemarkParams.value.Mark = "";
        setOrderRemarkParams.value.PrescriptionId = "";
      } else {
        ElNotification.warning(res.Content);
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};
const handleChangeAreas = async () => {
  if (!areas.value) return;
  const cityCodes = JSON.parse(areas.value);
  const res = await Passport_Api.getListByRegion({
    PageIndex: 1,
    PageSize: 9000,
    RegionCodes: [cityCodes[1]],
  });
  if (res.Type === 200) {
    organizationList.value = res.Data.Rows;
  } else {
    organizationList.value = [];
  }
};

const handleGetAddress = (row: TherapyOrder): string => {
  if (!row.TreatOrder || !row.TreatOrder.OrderAddresss || !row.TreatOrder.OrderAddresss.length) {
    return "";
  }
  return (
    row.TreatOrder.OrderAddresss[0].Name +
    " " +
    row.TreatOrder.OrderAddresss[0].Tel +
    " " +
    row.TreatOrder.OrderAddresss[0].ProvinceName +
    row.TreatOrder.OrderAddresss[0].CityName +
    row.TreatOrder.OrderAddresss[0].CountyName +
    row.TreatOrder.OrderAddresss[0].Address
  );
};

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handlePreviewOrEdit = () => {
  dialogVisible.value.create = true;
};
const handleCreateOrder = () => {
  const params = createOrderRef.value?.handleSubmit();
  if (!params) return;
  dialogConfirmLoading.value = true;
  Consult_Api.toExecute(params)
    .then((res) => {
      if (res.Type === 200) {
        ElNotification.success(res.Content);
        dialogVisible.value.create = false;
        handleGetTableList();
      } else {
        ElNotification.warning(res.Content);
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const copyData: GetOrderManageInputDTO = JSON.parse(JSON.stringify(queryParams.value));
  // 如果选择了区域 但是没有选择机构 就把所有机构传递给后端
  if (!copyData.OrgIds && areas.value) {
    copyData.OrgIds = organizationList.value.map((item) => item.Id);
  }
  const res = await Consult_Api.getOrderManage(copyData);
  if (res.Type === 200) {
    res.Data.Data.forEach((item) => {
      item.CreatedTime = dayjs(item.CreatedTime).format("YYYY-MM-DD HH:mm:ss");
    });
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};

const handleGetConsumablesName = (consumables: { Name: string; Count: number; Spec: string }[]) => {
  if (!consumables || !consumables.length) return "";
  return consumables.map((item) => (item.Spec || item.Name) + "*" + item.Count).join(",");
};

const handleGetTotalAmount = (row: TherapyOrder) => {
  return calculateAmount([row.TreatAmount, row.DeviceAmount || 0], "+");
};

const handleChangeOrg = () => {
  queryParams.value.DeptIds = null;
  queryParams.value.DoctorIds = null;
};

const fetchInstrumentTypeList = async () => {
  const list = await getInstrumentTypeList({
    page: 1,
    pageSize: 9000,
    isEnable: true,
  });
  instrumentList.value = list;
};

const handleExportExcel = async () => {
  const newData = {
    ServiceExportCode: "OrderManageReport", //后端
    ExportWay: ExportEnum.ServiceInvoke, // O:Redash, 1：后端
    ExecutingParams: queryParams.value,
    FileName: `治疗订单查询-${Date.now()}.xlsx`,
  };
  exportLoading.value = true;
  const res = await Report_Api.createExportTask(newData);
  if (res.Type === 200) {
    const notify = ElNotification.success({
      message: "<p>已加入下载任务，详情请在上方下载列表查看详情</p><br /><span>点击前往查看</span>",
      dangerouslyUseHTMLString: true,
      duration: 0,
      onClick: () => {
        EventBus.emit("triggerDownloadTask");
        notify.close();
      },
    });
  } else {
    ElNotification.warning(res.Content);
  }
  exportLoading.value = false;
};

const handleGetDeviceName = (deviceOrder: TherapyDeviceOrder) => {
  if (!deviceOrder || !deviceOrder.OrderDetails || !deviceOrder.OrderDetails.length) return "";
  return deviceOrder.OrderDetails.map((item) => item.Title).join(",");
};

const fetchConsumableList = async () => {
  const list = await getConsumablesTypeList({
    page: 1,
    pageSize: 9000,
    isEnable: true,
  });
  const filterListData = list.map((s) => s.Name);
  consumableList.value = [...new Set(filterListData)];
};

const handleInitQueryParams = (query: any) => {
  if (query.states) {
    queryParams.value.States = [Number(query.states)];
  }
};

onBeforeMount(() => {
  // 获取浏览器链接后面的参数
  const route = useRoute();
  // 直接获取 params
  handleInitQueryParams(route.query);
});

onMounted(() => {
  // 获取设备类型数据
  fetchInstrumentTypeList();
  // 获取耗材数据
  fetchConsumableList();
});
onActivated(() => {
  handleGetTableList();
});

watch(timeRange, (newVal) => {
  queryParams.value.BeginTime = newVal[0] + " 00:00:00";
  queryParams.value.EndTime = newVal[1] + " 23:59:59";
});
</script>

<style lang="scss" scoped></style>
