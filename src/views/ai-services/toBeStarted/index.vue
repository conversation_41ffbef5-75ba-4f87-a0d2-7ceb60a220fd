<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <!-- <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item> -->
              <el-form-item label="方案号">
                <el-input
                  v-model="queryParams.PrescriptionId"
                  type="link"
                  placeholder="方案号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <!-- <el-table-column label="流程Id" prop="FlowId" align="center" width="240" /> -->
          <el-table-column label="方案号" prop="PrescriptionIdStr" align="center" />
          <el-table-column label="订单号" prop="TreatOrderNoStr" align="center" />
          <el-table-column label="类型" align="center" width="150">
            <template #default="scope">
              {{ flowStateEnum[scope.row.Type] }}
            </template>
          </el-table-column>
          <el-table-column label="患者名称" prop="PatientName" width="150" align="center" />
          <el-table-column
            label="患者手机号"
            prop="PatientPhoneNumber"
            width="160"
            align="center"
          />
          <!-- <el-table-column label="流程状态" align="center" width="130">
            <template #default="scope">
              {{ stateEnum[scope.row.Status] }}
            </template>
          </el-table-column>
          <el-table-column label="最新状态" prop="LastMessage" align="center">
            <template #default="scope">
              <div style="text-align: left">{{ scope.row.LastMessage }}</div>
            </template>
          </el-table-column> -->
          <el-table-column
            label="创建时间"
            prop="CreatedTime"
            align="center"
            width="180"
            :formatter="tableDateFormat"
          />
          <el-table-column label="是否测试数据" prop="IsTest" align="center" width="180">
            <template #default="scope">
              <el-tag v-if="scope.row.IsTest" type="success">是</el-tag>
              <el-tag v-else type="danger">否</el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column label="AI客服" prop="UseCustomerService" align="center" width="140">
            <template #default="scope">
              <el-switch v-model="scope.row.UseCustomerService" disabled />
            </template>
          </el-table-column> -->
          <el-table-column label="操作" align="center" width="140">
            <template #default="scope">
              <el-button type="primary" link @click="handleStartFlow(scope.row)">开启</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageInput.PageIndex"
          v-model:limit="queryParams.PageInput.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
const flowStateEnum = ["磁疗", "脊柱", "足底"];
import { useTableConfig } from "@/hooks/useTableConfig";
import { FlowItem, FlowListInputDTO } from "@/api/aipowered/types";
import AIPowered_Api from "@/api/aipowered";

defineOptions({
  name: "AiServicesToBeStarted",
});

const queryParams = ref<FlowListInputDTO>({
  PrescriptionId: null,
  PhoneNumber: null,
  Status: null,
  Type: null,
  PageInput: {
    PageIndex: 1,
    PageSize: 20,
  },
});
// const timeRange = ref<[string, string]>([
//   dayjs().format("YYYY-MM-01"),
//   dayjs().format("YYYY-MM-DD"),
// ]);

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const { tableLoading, pageData, total, tableRef, tableDateFormat, tableFluidHeight, tableResize } =
  useTableConfig<FlowItem>();

const handleQuery = () => {
  queryParams.value.PageInput.PageIndex = 1;
  handleGetTableList();
};

const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await AIPowered_Api.getPendingFlowList(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Rows;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

const handleStartFlow = (row: FlowItem) => {
  ElMessageBox.confirm("确定启动该流程吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await AIPowered_Api.launchFlow({ PrescriptionId: row.PrescriptionIdStr! });
      if (res.Type === 200) {
        ElMessage.success("启动成功");
        handleGetTableList();
      }
    })
    .catch((err) => {
      ElMessage.error(err.Message);
    });
};

// watch(timeRange, (newVal) => {
//   queryParams.value.QueryStartDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
//   queryParams.value.QueryEndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
// });

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
