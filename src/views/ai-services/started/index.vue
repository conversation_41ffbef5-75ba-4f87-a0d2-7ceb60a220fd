<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="方案号">
                <el-input
                  v-model="queryParams.PrescriptionId"
                  type="link"
                  placeholder="方案号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="手机号">
                <el-input
                  v-model="queryParams.PhoneNumber"
                  type="link"
                  placeholder="手机号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="状态">
                <el-select
                  v-model="queryParams.Status"
                  clearable
                  placeholder="状态"
                  style="width: 100px"
                >
                  <el-option
                    v-for="(item, index) in stateEnum"
                    :key="index"
                    :label="item"
                    :value="index"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="类型">
                <el-select
                  v-model="queryParams.Type"
                  clearable
                  placeholder="类型"
                  style="width: 100px"
                >
                  <el-option
                    v-for="(item, index) in flowStateEnum"
                    :key="index"
                    :label="item"
                    :value="index"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column label="流程Id" prop="FlowId" align="center" width="240" />
          <el-table-column label="类型" align="center" width="100">
            <template #default="scope">
              {{ flowStateEnum[scope.row.Type] }}
            </template>
          </el-table-column>
          <el-table-column label="患者名称" prop="PatientName" width="100" align="center" />
          <el-table-column
            label="患者手机号"
            prop="PatientPhoneNumber"
            width="140"
            align="center"
          />
          <el-table-column label="流程状态" align="center" width="130">
            <template #default="scope">
              {{ stateEnum[scope.row.Status] }}
            </template>
          </el-table-column>
          <el-table-column label="最新状态" prop="LastMessage">
            <template #default="scope">
              <div style="text-align: left">{{ scope.row.LastMessage }}</div>
            </template>
          </el-table-column>
          <el-table-column label="启动时间" prop="LaunchTime" align="center" width="180" />
          <el-table-column label="AI客服" prop="UseCustomerService" align="center" width="140">
            <template #default="scope">
              <el-switch
                v-model="scope.row.UseCustomerService"
                @change="handleUseCustomerServiceChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="140">
            <template #default="scope">
              <el-button type="primary" link @click="handleOpenDialog(scope.row)">详情</el-button>
              <el-button
                v-if="scope.row.Status === 1"
                type="primary"
                link
                @click="handleSuspendOrResumeFlow(scope.row)"
              >
                暂停
              </el-button>
              <el-button
                v-if="scope.row.Status === 2"
                type="primary"
                link
                @click="handleSuspendOrResumeFlow(scope.row)"
              >
                恢复
              </el-button>
              <el-button
                v-if="scope.row.Status === 1"
                type="primary"
                link
                @click="handleTermination(scope.row)"
              >
                终止
              </el-button>
              <el-button
                v-if="scope.row.Status === 4 || scope.row.Status === 3"
                type="primary"
                link
                @click="handleRestart(scope.row)"
              >
                重启
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageInput.PageIndex"
          v-model:limit="queryParams.PageInput.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="700" destroy-on-close>
      <OAContent :prescriptionId="prescriptionId" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import AIPowered_Api from "@/api/aipowered";
import { FlowItem, FlowListInputDTO } from "@/api/aipowered/types";
import { useTableConfig } from "@/hooks/useTableConfig";

const stateEnum = ["未知", "进行中", "暂停", "结束(异常）", "结束(终止)", "结束(正常)"];
const flowStateEnum = ["磁疗", "脊柱", "足底"];

const dialogVisible = ref<boolean>(false);
const dialogTitle = ref<string>("");
const prescriptionId = ref<string>("");

defineOptions({
  name: "AiServicesStarted",
});

const queryParams = ref<FlowListInputDTO>({
  PrescriptionId: null,
  PhoneNumber: null,
  Status: null,
  Type: null,
  PageInput: {
    PageIndex: 1,
    PageSize: 20,
  },
});

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const { tableLoading, pageData, total, tableRef, tableDateFormat, tableFluidHeight, tableResize } =
  useTableConfig<FlowItem>();

const handleQuery = () => {
  queryParams.value.PageInput.PageIndex = 1;
  handleGetTableList();
};

const handleUseCustomerServiceChange = async (row: FlowItem) => {
  const res = await AIPowered_Api.switchAiCustomerService({
    PrescriptionId: row.PrescriptionIdStr!,
  });
  if (res.Type === 200) {
    ElMessage.success("操作成功");
  } else {
    ElMessage.error(res.Content);
  }
};

const handleSuspendOrResumeFlow = async (row: FlowItem) => {
  const type = row.Status === 1 ? "Suspend" : row.Status === 2 ? "Resume" : "";
  if (!type) return;

  let apiParams: { PrescriptionId: string; Reason?: string } = {
    PrescriptionId: row.PrescriptionIdStr!,
  };
  const dialogTitle: string = "提示";
  try {
    if (type === "Suspend") {
      const { value } = await ElMessageBox.prompt("请输入暂停原因", dialogTitle, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputValidator: (value) => {
          if (!value || value.trim() === "") {
            return "暂停原因不能为空";
          }
          return true;
        },
      });
      apiParams.Reason = value;
    } else {
      // type === 'Resume'
      await ElMessageBox.confirm("确定恢复流程吗？", dialogTitle, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      });
    }
    const res = await AIPowered_Api.suspendOrResumeFlow(apiParams, type);
    if (res.Type === 200) {
      ElMessage.success("操作成功");
      handleGetTableList();
    } else {
      ElMessage.error(res.Content);
    }
  } catch (err) {
    if (!String(err).toLowerCase().includes("cancel")) {
      ElMessage.error(typeof err === "string" ? err : (err as Error).message || "操作失败");
    }
  }
};

const handleTermination = async (row: FlowItem) => {
  const { value } = await ElMessageBox.prompt("请输入终止原因", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputValidator: (value) => {
      if (!value || value.trim() === "") {
        return "终止原因不能为空";
      }
      return true;
    },
  });
  const apiParams: { PrescriptionId: string; Reason?: string } = {
    PrescriptionId: row.PrescriptionIdStr!,
  };
  apiParams.Reason = value;
  const res = await AIPowered_Api.terminateFlow(apiParams);
  if (res.Type === 200) {
    ElMessage.success("操作成功");
    handleGetTableList();
  } else {
    ElMessage.error(res.Content);
  }
};

const handleRestart = async (row: FlowItem) => {
  const res = await AIPowered_Api.restartFlow({ PrescriptionId: row.PrescriptionIdStr! });
  if (res.Type === 200) {
    ElMessage.success("操作成功");
    handleGetTableList();
  } else {
    ElMessage.error(res.Content);
  }
};

const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await AIPowered_Api.getFlowList(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Rows;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

const handleOpenDialog = async (row: FlowItem) => {
  dialogTitle.value = `${row.PatientName}流程详情`;
  prescriptionId.value = row.PrescriptionIdStr!;
  dialogVisible.value = true;
};

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
