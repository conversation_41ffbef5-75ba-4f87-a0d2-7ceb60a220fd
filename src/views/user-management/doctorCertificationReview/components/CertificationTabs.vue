<template>
  <div v-loading="tabsLoading" class="p-20px overflow-y-auto">
    <el-tabs type="card">
      <el-tab-pane label="基本信息">
        <CertificationBasicInfo :data="tabsData" />
      </el-tab-pane>
      <el-tab-pane label="身份认证">
        <IdentityContent
          :user-certificates="tabsData.UserCertificates ?? []"
          :user-id="tabsData.UserId ?? ''"
          :disabled="true"
        />
      </el-tab-pane>
      <el-tab-pane label="职业认证">职业认证</el-tab-pane>
    </el-tabs>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormInstance } from "element-plus";
import Passport_Api from "@/api/passport";
import Dictionary_Api from "@/api/dictionary";
import { DoctorCertification } from "@/api/passport/types";
import { CertificationTabsData } from "../types/types";

const kEnableDebug = true;
const kFormRef = "ruleFormRef";

defineOptions({
  name: "CertificationForm",
});

const props = defineProps<{
  data: DoctorCertification;
  disabled: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  agree: [];
  reject: [];
}>();

onMounted(async () => {
  tabsLoading.value = true;
  const r0 = await requestBaseData();
  if (r0.Type !== 200) {
    tabsLoading.value = false;
    ElMessage.error(r0.Content);
    return r0;
  }

  const r1 = await requestAuthDoctorData();
  tabsLoading.value = false;
  if (r1.Type !== 200) {
    ElMessage.error(r1.Content);
  }
});

const tabsLoading = ref(false);
// 实例
const formRef = useTemplateRef<FormInstance>(kFormRef);
// 数据
const tabsData = ref<CertificationTabsData>({});

// 职称类型列表
const workerTypeList = ref<ReadDict[]>([]);
// 职位列表
const positionList = ref<ReadDict[]>([]);

// 提交表单
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      // requestAddOrUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields);
    }
  });
}

// 获取认证审核信息
async function requestAuthDoctorData() {
  const r = await Passport_Api.getSingleAuthDoctor(props.data.WorkflowId!);
  if (r.Type === 200) {
    tabsData.value = {
      ...props.data,
      ...r.Data,
      WorkerTypeName: workerTypeList.value.find((e) => e.Value === r.Data.WorkerType)?.Key,
    };
  }

  return r;
}

// 获取基础数据
async function requestBaseData() {
  const rs = await Promise.all([
    Dictionary_Api.getDict({ code: "WorkerTypeDict" }),
    Dictionary_Api.getDict({ code: "PositionDict", orgId: props.data.Organization!.Id! }),
  ]);

  const failed = rs.find((rs) => rs.Type !== 200);
  if (failed) {
    return failed;
  }

  workerTypeList.value = rs[0].Data;
  positionList.value = rs[1].Data;

  return rs[0];
}
</script>

<style lang="scss" scoped></style>
