<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
    <!-- 基本信息 -->
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="职业类型" prop="workerType">
          <el-select
            v-model="formData.workerType"
            class="w-full"
            @change="formData.workerTitle = ''"
          >
            <el-option
              v-for="item in workerTypeDictList"
              :key="item.Id"
              :label="item.Key"
              :value="item.Value!"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 医师认证信息 -->
    <template v-if="formData.workerType === 'doctor'">
      <el-divider content-position="left">医师认证信息</el-divider>

      <!-- 医师资格证 -->
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="医师资格证编号" prop="doctorQualify_Number">
            <el-input v-model="formData.doctorQualify_Number" placeholder="请输入医师资格证编号" />
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="医师资格证" prop="doctorQualify_Img">
            <MultiImageUpload v-model="formData.doctorQualify_Img" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 医师执业证 -->
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="医师执业证编号" prop="doctorPracticeQualify_Number">
            <el-input
              v-model="formData.doctorPracticeQualify_Number"
              placeholder="请输入医师执业证编号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="医师执业证" prop="doctorPracticeQualify_Img">
            <MultiImageUpload v-model="formData.doctorPracticeQualify_Img" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 职称证和电子执业证照 -->
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="职称证编码" prop="worker_title_Number">
            <el-input v-model="formData.worker_title_Number" placeholder="请输入职称证编码" />
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="电子执业证照" prop="electronicPractice_Img">
            <MultiImageUpload v-model="formData.electronicPractice_Img" />
          </el-form-item>
        </el-col>
      </el-row>
    </template>

    <!-- 治疗师认证信息 -->
    <template v-if="formData.workerType === 'therapist'">
      <el-divider content-position="left">治疗师认证信息</el-divider>

      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="卫生专业技术资格证书编号" prop="therapistQualify_Number">
            <el-input v-model="formData.therapistQualify_Number" placeholder="请输入资格证书编号" />
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="资格证" prop="therapistQualify_Img">
            <MultiImageUpload v-model="formData.therapistQualify_Img" />
          </el-form-item>
        </el-col>
      </el-row>
    </template>

    <!-- 护士认证信息 -->
    <template v-if="formData.workerType === 'nurse'">
      <el-divider content-position="left">护士认证信息</el-divider>

      <!-- 护士资格证 -->
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="护士资格证编号" prop="nurseQualify_Number">
            <el-input v-model="formData.nurseQualify_Number" placeholder="请输入护士资格证编号" />
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="护士资格证" prop="nurseQualify_Img">
            <MultiImageUpload v-model="formData.nurseQualify_Img" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 护士执业证 -->
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="护士执业证编号" prop="nursePracticeQualify_Number">
            <el-input
              v-model="formData.nursePracticeQualify_Number"
              placeholder="请输入护士执业证编号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="护士执业证" prop="nursePracticeQualify_Img">
            <MultiImageUpload v-model="formData.nursePracticeQualify_Img" />
          </el-form-item>
        </el-col>
      </el-row>
    </template>

    <!-- 职称证信息（非其他类型） -->
    <template v-if="formData.workerType !== 'other'">
      <el-divider content-position="left">职称信息</el-divider>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="职称" prop="workerTitle">
            <el-select v-model="formData.workerTitle" placeholder="请选择职称" class="w-full">
              <el-option
                v-for="(item, index) in workerTitleDict"
                :key="index"
                :label="item.Key!"
                :value="item.Value!"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="职称证" prop="worker_title_Img">
            <MultiImageUpload v-model="formData.worker_title_Img" />
          </el-form-item>
        </el-col>
      </el-row>
    </template>
    <!-- <el-button type="primary" @click="handleSubmit">提交</el-button> -->
  </el-form>
</template>

<script setup lang="ts">
import { UpsertAuthenticationInputDTO } from "@/api/passport/types";
import { FormInstance, FormRules } from "element-plus";

interface PageAuthenticationInfo extends Omit<UserCertificate, "CertificateValue"> {
  CertificateValue: string | string[];
}

const workerTitleDictList = inject("workerTitleDictList", ref<ReadDict[]>([]));
const workerTitleDict = computed(() => {
  const workerTypeId = workerTypeDictList.value.find(
    (item) => item.Value === formData.value.workerType
  )?.Id;
  return workerTitleDictList.value.filter((item) => item.ParentId === workerTypeId);
});
const workerTypeDictList = inject("workerTypeDictList", ref<ReadDict[]>([]));

const formRef = ref<FormInstance>();
const formData = ref<any>({
  doctorQualify_Number: "",
  doctorQualify_Img: [],
  doctorPracticeQualify_Number: "",
  doctorPracticeQualify_Img: [],
  worker_title_Number: "",
  electronicPractice_Img: [],
  therapistQualify_Number: "",
  therapistQualify_Img: [],
  nurseQualify_Number: "",
  nurseQualify_Img: [],
  nursePracticeQualify_Number: "",
  nursePracticeQualify_Img: [],
  worker_title_Img: [],
  workerType: "",
  workerTitle: "",
});
// 创建条件验证器工厂函数
const createConditionalValidator = (targetWorkerType: string, message: string) => {
  return (_rule: any, value: any, callback: any) => {
    if (formData.value.workerType === targetWorkerType) {
      if (!value || (Array.isArray(value) && value.length === 0) || value === "") {
        callback(new Error(message));
      } else {
        callback();
      }
    } else {
      callback();
    }
  };
};

// 职称验证器
const validateWorkerTitle = (_rule: any, _value: any, callback: any) => {
  if (
    formData.value.workerType !== "other" &&
    (!formData.value.workerTitle || formData.value.workerTitle === "")
  ) {
    callback(new Error("请选择职称"));
  } else {
    callback();
  }
};

const rules = reactive<FormRules>({
  // 职业类型必填
  workerType: [{ required: true, message: "请选择职业类型", trigger: "change" }],

  // 职称验证（医生、治疗师、护士都需要）
  workerTitle: [{ validator: validateWorkerTitle, trigger: "change" }],

  // 医生相关验证
  doctorQualify_Number: [
    {
      validator: createConditionalValidator("doctor", "请输入医师资格证编号"),
      trigger: "blur",
    },
  ],
  doctorQualify_Img: [
    {
      validator: createConditionalValidator("doctor", "请上传医师资格证"),
      trigger: "change",
    },
  ],
  doctorPracticeQualify_Number: [
    {
      validator: createConditionalValidator("doctor", "请输入医师执业证编号"),
      trigger: "blur",
    },
  ],
  doctorPracticeQualify_Img: [
    {
      validator: createConditionalValidator("doctor", "请上传医师执业证"),
      trigger: "change",
    },
  ],

  // 治疗师相关验证
  therapistQualify_Number: [
    {
      validator: createConditionalValidator("therapist", "请输入卫生专业技术资格证书编号"),
      trigger: "blur",
    },
  ],
  therapistQualify_Img: [
    {
      validator: createConditionalValidator("therapist", "请上传卫生专业技术资格证书"),
      trigger: "change",
    },
  ],

  // 护士相关验证
  nurseQualify_Number: [
    {
      validator: createConditionalValidator("nurse", "请输入护士资格证编号"),
      trigger: "blur",
    },
  ],
  nurseQualify_Img: [
    {
      validator: createConditionalValidator("nurse", "请上传护士资格证"),
      trigger: "change",
    },
  ],
  nursePracticeQualify_Number: [
    {
      validator: createConditionalValidator("nurse", "请输入护士执业证编号"),
      trigger: "blur",
    },
  ],
  nursePracticeQualify_Img: [
    {
      validator: createConditionalValidator("nurse", "请上传护士执业证"),
      trigger: "change",
    },
  ],
});
const handleSubmit = (): Promise<UpsertAuthenticationInputDTO | null> => {
  return new Promise((resolve) => {
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        let userCertificates: UserCertificate[] = [];
        // 将表单数据转换为AuthenticationInfo格式
        Object.entries(formData.value).forEach(([key, value]) => {
          let certValue: string = "";
          if (value === null || value === undefined) {
            certValue = "";
          } else if (typeof value === "object") {
            certValue = JSON.stringify(value);
          } else {
            certValue = String(value);
          }

          const item: UserCertificate = {
            CertificateType: key,
            CertificateValue: certValue,
          };
          userCertificates.push(item);
        });

        // 添加props.professionalCertification中的idCard相关证书
        if (props.professionalCertification && props.professionalCertification.length > 0) {
          const idCardCertificates = props.professionalCertification
            .filter(
              (cert) =>
                cert.CertificateType === "idCard" ||
                cert.CertificateType === "idCard_front" ||
                cert.CertificateType === "idCard_back"
            )
            .map((cert) => ({
              CertificateType: cert.CertificateType,
              CertificateValue: cert.CertificateValue,
              // 删除UserId字段
            }));
          userCertificates.push(...idCardCertificates);
        }
        // userCertificates删除 WorkerTitle、 WorkerType
        userCertificates = userCertificates.filter(
          (item) => item.CertificateType !== "workerTitle" && item.CertificateType !== "workerType"
        );
        resolve({
          UpsertUserCertificateMode: 1,
          UserCertificates: userCertificates,
          UserId: props.professionalCertification[0].UserId!,
          WorkerTitle: formData.value.workerTitle,
          WorkerType: formData.value.workerType,
        });
      } else {
        resolve(null);
      }
    });
  });
};

const handleProcessData = (newVal: UserCertificate[]) => {
  formData.value.workerTitle = props.workerTitle;
  formData.value.workerType = props.workerType;
  const currentList: PageAuthenticationInfo[] = newVal
    .filter(
      (s) =>
        s.CertificateType !== "idCard" &&
        s.CertificateType !== "idCard_front" &&
        s.CertificateType !== "idCard_back"
    )
    .map((v): PageAuthenticationInfo => {
      return {
        ...v,
        CertificateValue: v.CertificateType.includes("Img")
          ? typeof v.CertificateValue === "string"
            ? (() => {
                try {
                  return JSON.parse(v.CertificateValue);
                } catch {
                  return [v.CertificateValue];
                }
              })()
            : v.CertificateValue
          : v.CertificateValue || "",
      };
    });

  // 将currentList的内容转换为formData的内容
  currentList.forEach((item) => {
    const { CertificateType, CertificateValue } = item;
    if (formData.value.hasOwnProperty(CertificateType)) {
      formData.value[CertificateType] = CertificateValue;
    }
  });
};

interface Props {
  professionalCertification: UserCertificate[];
  workerType: string;
  workerTitle: string;
}
const props = defineProps<Props>();
watch(
  () => props.professionalCertification,
  async (newVal) => {
    if (!newVal || !newVal.length) {
      return;
    }
    handleProcessData(newVal);
  },
  { immediate: true }
);
defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-divider) {
  margin: 20px 0 16px 0;
}

:deep(.el-divider__text) {
  font-weight: 500;
  color: #409eff;
}

:deep(.el-upload-list--picture-card) {
  --el-upload-list-picture-card-size: 100px;
}

:deep(.el-upload--picture-card) {
  --el-upload-picture-card-size: 100px;
}

.w-full {
  width: 100%;
}
</style>
