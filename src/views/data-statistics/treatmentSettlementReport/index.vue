<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间段">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  unlink-panels
                  class="w-300px!"
                />
              </el-form-item>
              <el-form-item label="地区">
                <MultipleCityPicker v-model="selectedAreaCodeList" />
              </el-form-item>
              <el-form-item label="医院">
                <KSelect
                  v-model="queryParams.orgIdList"
                  multiple
                  collapse-tags
                  clearable
                  filterable
                  :data="hospitalList"
                  :loading="hospitalLoading"
                  :props="{ label: 'Name', value: 'Id' }"
                  :show-all="true"
                  @change="
                    () => {
                      queryParams.doctorIdList = undefined;
                      queryParams.deptIdList = undefined;
                      queryParams.therapistIdList = undefined;
                      queryParams.nurseIdList = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect
                  v-model="queryParams.deptIdList"
                  multiple
                  collapse-tags
                  clearable
                  filterable
                  :scopeable="true"
                  :org-id="queryParams.orgIdList?.[0]"
                  :disabled="queryParams.orgIdList?.length !== 1"
                  @change="
                    () => {
                      queryParams.doctorIdList = undefined;
                      queryParams.therapistIdList = undefined;
                      queryParams.nurseIdList = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="医助">
                <UserSelect
                  v-model="queryParams.assistantIdList"
                  multiple
                  collapse-tags
                  clearable
                  filterable
                  placeholder="请选择医助"
                  :scopeable="true"
                  :role-types="['assistant']"
                />
              </el-form-item>
              <el-form-item label="医生">
                <UserSelect
                  v-model="queryParams.doctorIdList"
                  placeholder="请选择医生"
                  multiple
                  collapse-tags
                  clearable
                  filterable
                  :org-ids="queryParams.orgIdList"
                  :dept-ids="queryParams.deptIdList"
                  :scopeable="true"
                  :role-types="['doctor']"
                />
              </el-form-item>
              <el-form-item label="治疗师">
                <UserSelect
                  v-model="queryParams.therapistIdList"
                  placeholder="请选择治疗师"
                  multiple
                  collapse-tags
                  clearable
                  filterable
                  :org-ids="queryParams.orgIdList"
                  :dept-ids="queryParams.deptIdList"
                  :scopeable="true"
                  :role-types="['therapist']"
                />
              </el-form-item>
              <el-form-item label="护士">
                <UserSelect
                  v-model="queryParams.nurseIdList"
                  placeholder="请选择护士"
                  multiple
                  collapse-tags
                  clearable
                  filterable
                  :org-ids="queryParams.orgIdList"
                  :dept-ids="queryParams.deptIdList"
                  :scopeable="true"
                  :role-types="['nurse']"
                />
              </el-form-item>
              <el-form-item label="是否测试数据">
                <KSelect
                  v-model="queryParams.isTest"
                  :data="[
                    { label: '否', value: '否' },
                    { label: '是', value: '是' },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="订单编号">
                <el-input
                  v-model="queryParams.orderNo"
                  placeholder="请输入订单编号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              :disabled="pageData.length === 0"
              :loading="exportLoading"
              @click="onExport"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <!-- 统计数据 -->
        <div class="text-20px font-600 h-40px">
          免费方案数
          <span style="color: red">
            {{ statisticsData.freePrescriptionCount ? statisticsData.freePrescriptionCount : 0 }}
          </span>
          条，付费方案数
          <span style="color: red">
            {{ statisticsData.payPrescriptionCount ? statisticsData.payPrescriptionCount : 0 }}
          </span>
          条;总计
          <span style="color: red">
            {{ statisticsData.totalPayAmount ? statisticsData.totalPayAmount : 0 }}
          </span>
          元，已退费
          <span style="color: red">
            {{ statisticsData.totalRefundAmount ? statisticsData.totalRefundAmount : 0 }}
          </span>
          元，下达人结算
          <span style="color: red">
            {{ statisticsData.creatorAmount ? statisticsData.creatorAmount : 0 }}
          </span>
          元，指导人结算
          <span style="color: red">
            {{ statisticsData.guideAmount ? statisticsData.guideAmount : 0 }}
          </span>
          元
        </div>

        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="Id"
          :height="tableFluidHeight - 40"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
        >
          <el-table-column prop="tradeType" label="类型" width="80" show-overflow-tooltip />
          <el-table-column prop="orderNo" label="订单号" min-width="150" show-overflow-tooltip />
          <el-table-column
            prop="orderCreatedTime"
            label="订单日期"
            width="120"
            show-overflow-tooltip
          />
          <el-table-column prop="optTime" label="付款/退款日期" width="120" show-overflow-tooltip />
          <el-table-column
            prop="tradeAlias"
            min-width="70"
            label="支付方式"
            show-overflow-tooltip
          />
          <el-table-column prop="Remark" min-width="180" label="备注" show-overflow-tooltip />
          <el-table-column prop="cityName" min-width="80" label="地区" show-overflow-tooltip />
          <el-table-column
            prop="organizationName"
            min-width="150"
            label="医院"
            show-overflow-tooltip
          />
          <el-table-column
            prop="assistantName"
            min-width="120"
            label="医助姓名"
            show-overflow-tooltip
          />
          <el-table-column
            prop="patientInfo"
            label="就诊人信息"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="treatmentName"
            label="治疗项目"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="treatmentUnitPrice"
            label="治疗项目单价"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column prop="chargeUnit" label="计价单位" width="80" show-overflow-tooltip />
          <el-table-column
            prop="treatmentItemsCount"
            label="治疗项目数量（以计价单位计算）"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column prop="Amount" label="金额" width="80" show-overflow-tooltip />
          <el-table-column prop="state" label="方案状态" width="80" show-overflow-tooltip />
          <el-table-column prop="hasSettle" label="是否已打款" width="80" show-overflow-tooltip />
          <el-table-column prop="doctorName" label="医生姓名" width="80" show-overflow-tooltip />
          <el-table-column
            prop="doctorPhoneNumber"
            label="医生联系方式"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="doctorSettleRatio"
            label="医生结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="doctorSettleAmount"
            label="医生结算金额"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="doctorBankCardNo"
            label="医生银行卡号"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="doctorBankName"
            label="医生开户行"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="doctorSubBranch"
            label="医生开户支行"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column
            prop="therapistName"
            label="治疗师姓名"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="therapistNumber"
            label="治疗师联系方式"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="therapistSettleRatio"
            label="治疗师结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="therapistSettleAmount"
            label="治疗师结算金额"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="therapistBankCardNo"
            label="治疗师银行卡号"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="therapistBankName"
            label="治疗师开户行"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="therapistSubBranch"
            label="治疗师开户支行"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column prop="nurseName" label="护士姓名" width="80" show-overflow-tooltip />
          <el-table-column
            prop="nursePhoneNumber"
            label="护士联系方式"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="nurseSettleRatio"
            label="护士结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="nurseSettleAmount"
            label="护士结算金额"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="nurseBankCardNo"
            label="护士银行卡号"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="nurseBankName"
            label="护士开户行"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="nurseSubBranch"
            label="护士开户支行"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column prop="MarketingName" label="市场开发" width="80" show-overflow-tooltip />
          <el-table-column
            prop="MarketingSettleRatio"
            label="市场开发结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="MarketingSettleAmount"
            label="市场开发结算金额"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column prop="AssistantName" label="医助" width="120" show-overflow-tooltip />
          <el-table-column
            prop="AssistantSettleRatio"
            label="医助结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="AssistantSettleAmount"
            label="医助结算金额"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="AssistantManagerName"
            label="医助管理"
            width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="AssistantManagerSettleRatio"
            label="医助管理结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="AssistantManagerSettleAmount"
            label="医助管理结算金额"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="OnlineGuidanceName"
            label="上线人员"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="OnlineGuidanceSettleRatio"
            label="上线人员结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="OnlineGuidanceSettleAmount"
            label="上线人员结算金额"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column prop="DealerName" label="经销商" min-width="80" show-overflow-tooltip />
          <el-table-column
            prop="DealerSettleRatio"
            label="经销商结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="DealerSettleAmount"
            label="经销商结算金额"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column prop="ReferrerName" label="推荐人" width="80" show-overflow-tooltip />
          <el-table-column
            prop="ReferrerSettleRatio"
            label="推荐人结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="ReferrerSettleAmount"
            label="推荐人结算金额"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column prop="OtherName" label="其他" width="80" show-overflow-tooltip />
          <el-table-column
            prop="OtherSettleRatio"
            label="其他结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="OtherSettleAmount"
            label="其他结算金额"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="nurseChiefName"
            label="护士长姓名"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="nurseChiefPhoneNumber"
            label="护士长联系方式"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="nurseChiefSettleRatio"
            label="护士长结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="nurseChiefSettleAmount"
            label="护士长结算金额"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="nurseChiefBankCardNo"
            label="护士长银行卡号"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="nurseChiefBankName"
            label="护士长开户行"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="nurseChiefSubBranch"
            label="护士长开户支行"
            width="180"
            show-overflow-tooltip
          />
          <el-table-column
            prop="deptDirectorName"
            label="科主任姓名"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="deptDirectorPhoneNumber"
            label="科主任联系方式"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="deptDirectorSettleRatio"
            label="科主任结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="deptDirectorSettleAmount"
            label="科主任结算金额"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="deptDirectorBankCardNo"
            label="科主任银行卡号"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="deptDirectorBankName"
            label="科主任开户行"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="deptDirectorSubBranch"
            label="科主任开户支行"
            width="180"
            show-overflow-tooltip
          />
          <el-table-column
            prop="inspectorName"
            label="检测人员姓名"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="inspectorPhoneNumber"
            label="检测人员联系方式"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="inspectorSettleRatio"
            label="检测人员结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="inspectorSettleAmount"
            label="检测人员结算金额"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="inspectorBankCardNo"
            label="检测人员银行卡号"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="inspectorBankName"
            label="检测人员开户行"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="inspectorSubBranch"
            label="检测人员开户支行"
            width="180"
            show-overflow-tooltip
          />
          <el-table-column
            prop="hospitalDirectorName"
            label="院长姓名"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="hospitalDirectorPhoneNumber"
            label="院长联系方式"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="hospitalDirectorSettleRatio"
            label="院长结算比例"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="hospitalDirectorSettleAmount"
            label="院长结算金额"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="hospitalDirectorBankCardNo"
            label="院长银行卡号"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="hospitalDirectorBankName"
            label="院长开户行"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="hospitalDirectorSubBranch"
            label="院长开户支行"
            width="180"
            show-overflow-tooltip
          />
          <el-table-column
            prop="recommendedDoctorName"
            label="患者推荐人（医生）"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="recommendedDoctorPhoneNumber"
            label="患者推荐人（医生）手机号"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="recommendedNormalUserName"
            label="患者推荐人"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="recommendedNormalUserPhoneNumber"
            label="患者推荐人手机号"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="prescriptionSellerName"
            label="方案销售"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="prescriptionSellerPhoneNumber"
            label="方案销售手机号"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column label="是否测试数据" prop="isTest" width="70" show-overflow-tooltip />
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { convertToRedashParams, exportExcel, getExportCols } from "@/utils/serviceUtils";
import Report_Api from "@/api/report";
import { ExportEnum } from "@/enums/Other";
import { ExportTaskRedashDTO, TreatmentSettlementReportParams } from "@/api/report/types";
import { useUserStore } from "@/store";
import Passport_Api from "@/api/passport";

interface QueryParams extends RedashParameters<TreatmentSettlementReportParams> {
  orgIdList?: string[];
  deptIdList?: string[];
  doctorIdList?: string[];
  nurseIdList?: string[];
  therapistIdList?: string[];
  assistantIdList?: string[];
}

// 调试开关
const kEnableDebug = true;
defineOptions({
  name: "TreatmentSettlementReport",
});

const {
  kTableRef,
  tableRef,
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  exportLoading,
} = useTableConfig<TreatmentSettlementReport>();

// 查询结果ID
let queryResultId = -1;

// 统计数据
const statisticsData = ref<TreatmentSettlementReportStatistics>({});

// 查询条件
const queryParams = reactive<QueryParams>({
  optTimeStart: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
  optTimeEnd: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  LoginUserId: useUserStore().userInfo.Id,
  assistantUserIds: undefined,
  deptIds: undefined,
  doctorUserIds: undefined,
  nurseUserIds: undefined,
  therapistUserIds: undefined,
  isTest: "否",
  orderNo: undefined,
  orgIds: undefined,
  pageIndex: 1,
  pageSize: 10,
});

// 定义 dateRange
const dateRange = computed({
  get() {
    // 从 queryParams 中获取日期范围
    return [queryParams.optTimeStart!, queryParams.optTimeEnd!];
  },
  set(newValue) {
    // 当用户选择日期范围时，更新 queryParams
    if (newValue && newValue.length === 2) {
      kEnableDebug && console.debug("newValue", newValue);
      queryParams.optTimeStart = newValue[0].split(" ")[0] + " 00:00:00";
      queryParams.optTimeEnd = newValue[1].split(" ")[0] + " 23:59:59";
    }
  },
});

watch(
  () => queryParams.optTimeStart,
  () => {
    queryParams.BeginTimeDt = queryParams.optTimeStart;
  },
  { immediate: true }
);

watch(
  () => queryParams.optTimeEnd,
  () => {
    queryParams.EndTimeDt = queryParams.optTimeEnd;
  },
  { immediate: true }
);

watch(
  () => queryParams.orgIdList,
  () => {
    if (!queryParams.orgIdList?.length) {
      queryParams.orgIds = undefined;
    } else {
      queryParams.orgIds = queryParams.orgIdList.join(",");
    }
  }
);

watch(
  () => queryParams.deptIdList,
  () => {
    if (!queryParams.deptIdList?.length) {
      queryParams.deptIds = undefined;
    } else {
      queryParams.deptIds = queryParams.deptIdList.join(",");
    }
  }
);

watch(
  () => queryParams.doctorIdList,
  () => {
    if (!queryParams.doctorIdList?.length) {
      queryParams.doctorUserIds = undefined;
    } else {
      queryParams.doctorUserIds = queryParams.doctorIdList.join(",");
    }
  }
);

watch(
  () => queryParams.therapistIdList,
  () => {
    if (!queryParams.therapistIdList?.length) {
      queryParams.therapistUserIds = undefined;
    } else {
      queryParams.therapistUserIds = queryParams.therapistIdList.join(",");
    }
  }
);

watch(
  () => queryParams.nurseIdList,
  () => {
    if (!queryParams.nurseIdList?.length) {
      queryParams.nurseUserIds = undefined;
    } else {
      queryParams.nurseUserIds = queryParams.nurseIdList.join(",");
    }
  }
);

watch(
  () => queryParams.assistantIdList,
  () => {
    if (!queryParams.assistantIdList?.length) {
      queryParams.assistantUserIds = undefined;
    } else {
      queryParams.assistantUserIds = queryParams.assistantIdList.join(",");
    }
  }
);

// 医院列表
let allHospitalList: BaseOrganization[] = [];
const hospitalList = ref<BaseOrganization[]>([]);
const hospitalLoading = ref(false);

// 选择的地区code列表
const selectedAreaCodeList = ref<string[][]>([]);
watch(
  selectedAreaCodeList,
  () => {
    kEnableDebug && console.debug("selectedAreaCodeList", selectedAreaCodeList.value);
    requestHospitalList();
  },
  { immediate: true }
);

// 点击搜索
function handleQuery() {
  queryParams.pageIndex = 1;
  refreshQueryData();
}

// 点击导出
async function onExport() {
  kEnableDebug && console.debug("点击导出");

  const {
    orgIdList,
    deptIdList,
    doctorIdList,
    nurseIdList,
    therapistIdList,
    assistantIdList,
    BeginTimeDt,
    EndTimeDt,
    ...parameters
  } = queryParams;
  const exportParams = convertToRedashParams(parameters, "Report_SettlementDetail");
  const params: ExportTaskRedashDTO = {
    Cols: getExportCols(tableRef.value!.columns as any, "@"),
    ExecutingParams: exportParams.parameters,
    ExportWay: ExportEnum.PlainMySql,
    FileName: `财务结算报表(治疗)-${Date.now()}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.pageIndex,
    PageSize: queryParams.pageSize,
    QueryName: "Report_SettlementDetail",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
}

// 刷新查询数据
async function refreshQueryData() {
  tableLoading.value = true;
  const rs = await Promise.all([requestTableList(), requestStatisticsData()]);
  tableLoading.value = false;

  const failed = rs.find((r) => r.Type !== 200);
  if (failed) {
    ElMessage.error(failed.Content);
  }
}

// 请求列表数据
async function requestTableList() {
  const {
    orgIdList,
    deptIdList,
    doctorIdList,
    nurseIdList,
    therapistIdList,
    assistantIdList,
    BeginTimeDt,
    EndTimeDt,
    ...parameters
  } = queryParams;
  const params = convertToRedashParams(parameters, "Report_SettlementDetail");
  const r = await Report_Api.getRedashList<TreatmentSettlementReport>(params);
  if (r.Type === 200) {
    queryResultId = r.Data.QueryResultId;
    pageData.value = r.Data.Data;
    total.value = r.Data.TotalCount;
  }

  return r;
}

// 请求统计数据
async function requestStatisticsData() {
  const {
    orgIdList,
    deptIdList,
    doctorIdList,
    nurseIdList,
    therapistIdList,
    assistantIdList,
    optTimeStart,
    optTimeEnd,
    ...parameters
  } = queryParams;
  const params = convertToRedashParams(parameters, "Report_SettlementStatistics");
  const r = await Report_Api.getRedashList<TreatmentSettlementReportStatistics>(params);
  if (r.Type === 200 && r.Data.Data.length > 0) {
    statisticsData.value = r.Data.Data[0];
  }

  return r;
}

// 获取医院列表
async function requestHospitalList() {
  hospitalLoading.value = true;
  if (selectedAreaCodeList.value.length === 0) {
    // 如果已经获取过全量医院列表，则直接使用
    if (allHospitalList.length > 0) {
      hospitalList.value = allHospitalList;
      hospitalLoading.value = false;
      return { Type: 200 };
    }

    // 获取全量医院列表
    const r = await Passport_Api.getOrganizationList({
      IsEnabled: true,
      DtoTypeName: "QueryOrgDtoForDropDownList",
      Scopeable: true,
      Pageable: true,
      PageIndex: 1,
      PageSize: 99999,
    });
    if (r.Type === 200) {
      allHospitalList = r.Data.Rows;
      hospitalList.value = allHospitalList;
    }
    hospitalLoading.value = false;

    return r;
  }

  // 获取选择地区获取医院列表
  const regionCodes: string[] = [];
  selectedAreaCodeList.value.forEach((item) => {
    if (item.length > 0) {
      regionCodes.push(item[item.length - 1]);
    }
  });
  kEnableDebug && console.debug("regionCodes", regionCodes);
  const r = await Passport_Api.getListByRegion({
    PageIndex: 1,
    PageSize: 99999,
    IsEnabled: true,
    RegionCodes: regionCodes,
  });
  if (r.Type === 200) {
    hospitalList.value = r.Data.Rows;
  }
  hospitalLoading.value = false;

  return r;
}

onActivated(() => {
  refreshQueryData();
});
</script>

<style lang="scss" scoped></style>
