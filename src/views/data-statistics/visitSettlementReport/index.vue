<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="交易时间">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  unlink-panels
                  style="width: 250px"
                  :disabled-date="
                    (data: Date) => {
                      return data.getTime() > new Date().getTime();
                    }
                  "
                  :default-value="[dayjs().startOf('month').toDate(), dayjs().toDate()]"
                />
              </el-form-item>
              <el-form-item label="医院" prop="orgIdList">
                <HospitalSelect
                  v-model="queryParams.orgIdList"
                  :scopeable="true"
                  filterable
                  multiple
                  collapse-tags
                  clearable
                  @change="
                    () => {
                      queryParams.deptIdList = undefined;
                      queryParams.doctorUserIds = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="科室" prop="deptIdList">
                <DeptSelect
                  v-model="queryParams.deptIdList"
                  filterable
                  multiple
                  collapse-tags
                  clearable
                  placeholder="输入关键字筛选"
                  :org-id="queryParams.orgIdList?.[0]"
                  :disabled="queryParams.orgIdList?.length !== 1"
                  @change="
                    () => {
                      queryParams.doctorUserIds = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="接诊人" prop="doctorIdList">
                <UserSelect
                  v-model="queryParams.doctorIdList"
                  placeholder="请选择医生"
                  filterable
                  multiple
                  collapse-tags
                  clearable
                  :org-ids="queryParams.orgIdList"
                  :dept-ids="queryParams.deptIdList"
                  :role-types="['doctor']"
                />
              </el-form-item>
              <!-- 静态数据下拉单选 -->
              <el-form-item label="是否测试数据" prop="isTest">
                <KSelect
                  v-model="queryParams.isTest"
                  :data="[
                    { label: '否', value: '否' },
                    { label: '是', value: '是' },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="关键字" prop="keyword">
                <el-input
                  v-model="queryParams.keyword"
                  placeholder="订单编号/患者姓名"
                  prefix-icon="el-icon-Search"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              :disabled="pageData.length === 0"
              :loading="exportLoading"
              @click="onExport"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="Id"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
        >
          <el-table-column prop="tradeType" label="类型" min-width="60" />
          <el-table-column prop="orderNo" label="订单号" min-width="180" show-overflow-tooltip />
          <el-table-column prop="orderCreatedTime" label="订单创建时间" width="120" />
          <el-table-column prop="optTime" label="交易时间" width="120" />
          <el-table-column
            prop="TradeNo"
            label="微信支付业务单号"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column prop="Amount" label="交易金额" width="100" />
          <el-table-column
            prop="organizationName"
            label="医院"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column prop="doctorName" label="接诊人" min-width="70" />
          <el-table-column
            prop="patientInfo"
            label="患者信息"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column prop="assistantName" label="医助" min-width="70" />
          <el-table-column label="是否测试数据" prop="isTest" width="80" />
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { convertToRedashParams, exportExcel, getExportCols } from "@/utils/serviceUtils";
import Report_Api from "@/api/report";
import { ExportEnum } from "@/enums/Other";
import { ExportTaskRedashDTO, VisitSettlementReportParams } from "@/api/report/types";
import { useUserStore } from "@/store";

interface QueryParams extends VisitSettlementReportParams {
  orgIdList?: string[];
  deptIdList?: string[];
  doctorIdList?: string[];
}

// 调试开关
const kEnableDebug = true;
defineOptions({
  name: "VisitSettlementReport",
});

const {
  kTableRef,
  tableRef,
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  exportLoading,
} = useTableConfig<VisitSettlementReport>();

let queryResultId = -1;

// 查询条件
const queryParams = reactive<RedashParameters<QueryParams>>({
  optTimeStart: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
  optTimeEnd: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  orgIds: undefined,
  deptIds: undefined,
  doctorUserIds: undefined,
  assistantUserIds: "*",
  isTest: "否",
  keyword: undefined,
  LoginUserId: useUserStore().userInfo.Id,
  pageIndex: 1,
  pageSize: 10,
});

// 定义 dateRange
const dateRange = computed({
  get() {
    // 从 queryParams 中获取日期范围
    return [queryParams.optTimeStart, queryParams.optTimeEnd];
  },
  set(newValue) {
    // 当用户选择日期范围时，更新 queryParams
    if (newValue && newValue.length === 2) {
      queryParams.optTimeStart = newValue[0].split(" ")[0] + " 00:00:00";
      queryParams.optTimeEnd = newValue[1].split(" ")[0] + " 23:59:59";
    }
  },
});

watch(
  () => queryParams.orgIdList,
  () => {
    kEnableDebug && console.debug("queryParams.orgIdList", queryParams.orgIdList);
    if (!queryParams.orgIdList?.length) {
      queryParams.orgIds = undefined;
      return;
    }

    queryParams.orgIds = queryParams.orgIdList.join(",");
  }
);

watch(
  () => queryParams.deptIdList,
  () => {
    kEnableDebug && console.debug("queryParams.deptIdList", queryParams.deptIdList);
    if (!queryParams.deptIdList?.length) {
      queryParams.deptIds = undefined;
      return;
    }

    queryParams.deptIds = queryParams.deptIdList.join(",");
  }
);

watch(
  () => queryParams.doctorIdList,
  () => {
    kEnableDebug && console.debug("queryParams.doctorIdList", queryParams.doctorIdList);
    if (!queryParams.doctorIdList?.length) {
      queryParams.doctorUserIds = undefined;
      return;
    }

    queryParams.doctorUserIds = queryParams.doctorIdList.join(",");
  }
);

// 点击搜索
function handleQuery() {
  queryParams.pageIndex = 1;
  requestTableList();
}

// 点击导出
async function onExport() {
  kEnableDebug && console.debug("点击导出");

  const { orgIdList, deptIdList, doctorIdList, ...parameters } = queryParams;
  const exportParams = convertToRedashParams(parameters, "Report_SettlementDetail_Visit");
  const params: ExportTaskRedashDTO = {
    Cols: getExportCols(tableRef.value!.columns as any, "@"),
    ExecutingParams: exportParams.parameters,
    ExportWay: ExportEnum.PlainMySql,
    FileName: `财务结算报表(就诊)-${Date.now()}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.pageIndex,
    PageSize: queryParams.pageSize,
    QueryName: "Report_SettlementDetail_Visit",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const { orgIdList, deptIdList, doctorIdList, ...parameters } = queryParams;

  const params = convertToRedashParams(parameters, "Report_SettlementDetail_Visit");
  const r = await Report_Api.getRedashList<VisitSettlementReport>(params);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  queryResultId = r.Data.QueryResultId;
  pageData.value = r.Data.Data;
  total.value = r.Data.TotalCount;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
