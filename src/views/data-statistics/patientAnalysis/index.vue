<template>
  <div
    class="app-container overflow-y-auto bg-gray-100 dark:bg-dark-900 text-gray-800 dark:text-gray-200"
  >
    <div class="p-2 md:p-3 bg-white dark:bg-dark-800 rounded-lg shadow-md mb-2 md:mb-3">
      <el-form :model="topChartsQueryParams" label-position="right" :inline="true">
        <el-form-item label="机构">
          <HospitalSelect v-model="topChartsQueryParams.OrgIds" />
        </el-form-item>
        <el-form-item label="科室">
          <DeptSelect
            v-model="topChartsQueryParams.DeptIds"
            :org-id="topChartsQueryParams.OrgIds"
          />
        </el-form-item>
        <el-form-item label="医生/治疗师">
          <UserSelect
            v-model="topChartsQueryParams.Keyword"
            :org-ids="topChartsQueryParams.OrgIds ? [topChartsQueryParams.OrgIds] : null"
            :dept-ids="topChartsQueryParams.DeptIds ? [topChartsQueryParams.DeptIds] : null"
            :role-types="['doctor', 'therapist']"
            key-id="Name"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadPageData">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div
      v-loading="isLoading"
      class="content-container p-2 md:p-3 rounded-lg shadow-md bg-white dark:bg-dark-800"
    >
      <div class="flex flex-wrap -m-1">
        <div class="w-full md:w-1/2 p-1">
          <div
            style="height: 50px; text-align: center; line-height: 50px"
            class="text-lg font-semibold text-orange-500 dark:text-yellow-400"
          >
            累计患者：{{ totalPatientCount || 0 }}人
          </div>
          <div class="flex -m-1">
            <div class="w-1/2 p-1">
              <div
                ref="sexChartRef"
                class="h-300px p-1 bg-gray-50 dark:bg-dark-700 rounded-md shadow"
              />
            </div>
            <div class="w-1/2 p-1">
              <div
                ref="ageChartRef"
                class="h-300px p-1 bg-gray-50 dark:bg-dark-700 rounded-md shadow"
              />
            </div>
          </div>
        </div>
        <div class="w-full md:w-1/2 p-1">
          <div
            ref="diseaseChartRef"
            class="h-350px p-1 bg-gray-50 dark:bg-dark-700 rounded-md shadow"
          />
        </div>
      </div>
      <div
        class="flex items-center flex-wrap space-x-0 md:space-x-2 my-2 p-2 bg-white dark:bg-dark-800 rounded-lg shadow-md"
      >
        <FormItem label="视图模式">
          <el-radio-group v-model="bottomChartsViewMode">
            <el-radio label="1">日</el-radio>
            <el-radio label="2">周</el-radio>
            <el-radio label="3">月</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem label="时间段">
          <el-date-picker
            v-model="bottomChartsTimeRange"
            unlink-panels
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            :clearable="false"
            value-format="YYYY-MM-DD"
            style="width: 250px"
          />
        </FormItem>
        <FormItem>
          <el-button type="primary" @click="loadPageData">查询</el-button>
        </FormItem>
      </div>
      <div class="flex flex-wrap -m-1">
        <div class="w-full p-1">
          <div
            ref="prescriptionExecutionFeeChartRef"
            class="h-300px p-1 bg-gray-50 dark:bg-dark-700 rounded-md shadow"
          />
        </div>
        <div class="w-full p-1">
          <div
            ref="prescriptionExecutionStatusChartRef"
            class="h-300px p-1 bg-gray-50 dark:bg-dark-700 rounded-md shadow"
          />
        </div>
        <div class="w-full p-1">
          <div
            ref="patientDiagnosisStatsChartRef"
            class="h-300px p-1 bg-gray-50 dark:bg-dark-700 rounded-md shadow"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import reportApi from "@/api/report";
import {
  PatientDiagnoseStatisticsItem,
  PatientDiseaseTagStatisticsItem,
  PatientStatisticsItem,
  PrescriptionExecStatisticItem,
  PrescriptionSendStatisticItem,
} from "@/api/report/types";
import { convertToRedashParams } from "@/utils/serviceUtils";
import dayjs from "dayjs";
import { EChartsType } from "echarts";
import * as echarts from "echarts";
import { EpPropMergeTypeWithNull } from "element-plus";
import { onActivated, onDeactivated, nextTick, ref, watch } from "vue";

defineOptions({
  name: "PatientAnalysis",
});

interface TopChartsQueryParams {
  OrgIds: EpPropMergeTypeWithNull<string>;
  Keyword: string;
  DeptIds: EpPropMergeTypeWithNull<string>;
  PageIndex?: number;
  PageSize?: number;
  QueryStartDate?: string;
  QueryEndDate?: string;
}

interface BottomChartsQueryParams {
  OrgIds: EpPropMergeTypeWithNull<string>;
  Keyword: string;
  DeptId: EpPropMergeTypeWithNull<string>;
  PageIndex?: number;
  PageSize?: number;
  StartTimeDt?: string;
  EndTimeDt?: string;
  ViewMode?: string;
  OrgView?: string;
}

let diseaseChartInstance: EChartsType | undefined;
const diseaseChartRef = ref<HTMLDivElement>();
let sexChartInstance: EChartsType | undefined;
const sexChartRef = ref<HTMLDivElement>();
let ageChartInstance: EChartsType | undefined;
const ageChartRef = ref<HTMLDivElement>();
let prescriptionExecutionFeeChartInstance: EChartsType | undefined;
const prescriptionExecutionFeeChartRef = ref<HTMLDivElement>();
let prescriptionExecutionStatusChartInstance: EChartsType | undefined;
const prescriptionExecutionStatusChartRef = ref<HTMLDivElement>();
let patientDiagnosisStatsChartInstance: EChartsType | undefined;
const patientDiagnosisStatsChartRef = ref<HTMLDivElement>();

const isLoading = ref(false);

// ECharts theme configurations
const lightTheme = {
  color: [
    "#5470c6",
    "#91cc75",
    "#fac858",
    "#ee6666",
    "#73c0de",
    "#3ba272",
    "#fc8452",
    "#9a60b4",
    "#ea7ccc",
  ],
  backgroundColor: "rgba(255, 255, 255, 0)", // Transparent or light background
  textStyle: {},
  title: {
    // Default title style for light theme
    textStyle: {
      color: "#333",
    },
  },
  legend: {
    // Default legend style for light theme
    textStyle: {
      color: "#333",
    },
  },
  xAxis: {
    // Default X axis style for light theme
    axisLine: {
      lineStyle: {
        color: "#666",
      },
    },
    axisLabel: {
      color: "#666",
    },
  },
  yAxis: {
    // Default Y axis style for light theme
    axisLine: {
      lineStyle: {
        color: "#666",
      },
    },
    axisLabel: {
      color: "#666",
    },
  },
  toolbox: {
    iconStyle: {
      borderColor: "#666",
    },
  },
};

const darkTheme = {
  color: [
    "#80baff",
    "#95d475",
    "#fadc7d",
    "#f28d8d",
    "#82d9ff",
    "#5bc49f",
    "#ffab8c",
    "#b38fda",
    "#eb9de4",
  ],
  backgroundColor: "rgba(0,0,0,0)", // Transparent background for dark mode
  textStyle: {
    color: "#ccc",
  },
  title: {
    textStyle: {
      color: "#eee", // Brighter color for titles in dark mode
    },
  },
  legend: {
    textStyle: {
      color: "#ccc",
    },
  },
  tooltip: {
    textStyle: {
      color: "#fff", // Ensuring tooltip text is visible in dark inputs
    },
    backgroundColor: "rgba(50,50,50,0.7)",
    borderColor: "#333",
  },
  xAxis: {
    axisLine: {
      lineStyle: {
        color: "#888",
      },
    },
    axisLabel: {
      color: "#ccc",
    },
  },
  yAxis: {
    axisLine: {
      lineStyle: {
        color: "#888",
      },
    },
    axisLabel: {
      color: "#ccc",
    },
  },
  toolbox: {
    iconStyle: {
      borderColor: "#ccc",
    },
  },
  // Specific for bar charts in dark mode to ensure good contrast if needed
  // visualMap: {
  //   textStyle: {
  //     color: '#ccc'
  //   }
  // },
};

/**
 * @description 获取当前图表应使用的主题名称（"light" 或 "dark"）。
 * @returns {string} 主题名称。
 */
function getCurrentChartThemeName(): string {
  return document.documentElement.classList.contains("dark") ? "dark" : "light";
}

const bottomChartsTimeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

/**
 * @description 顶部图表区域的查询参数。
 */
const topChartsQueryParams = ref<TopChartsQueryParams>({
  OrgIds: null,
  Keyword: "",
  DeptIds: null,
  QueryStartDate: dayjs(bottomChartsTimeRange.value[0]).format("YYYY-MM-01 00:00:00"),
  QueryEndDate: dayjs(bottomChartsTimeRange.value[1]).format("YYYY-MM-DD 23:59:59"),
});

const totalPatientCount = ref<number>(0);
/**
 * @description 底部图表的视图模式 ("1": 日, "2": 周, "3": 月)。
 */
const bottomChartsViewMode = ref<string>("1");

// Helper function for fetching data using Redash
/**
 * @interface ApiResponse
 * @description API响应的通用结构。
 * @template TDataPayload API响应中Data字段的负载类型。
 */
interface ApiResponse<TDataPayload> {
  Type: number;
  Content?: string;
  Data: TDataPayload;
}

/**
 * @interface RedashDataWrapper
 * @description Redash API 返回的数据包装结构，实际数据在内嵌的Data数组中。
 * @template TItem 数据项的类型。
 */
interface RedashDataWrapper<TItem> {
  Data: TItem[];
}

/**
 * @description 通用的Redash数据获取辅助函数。
 * @template TItem 期望从Redash获取的数据项类型。
 * @template TParams 查询参数的类型。
 * @param {function} apiMethod 实际调用的API方法 (例如 reportApi.getRedashList)。
 * @param {TParams} baseQueryParams 基础查询参数。
 * @param {string} redashQueryName Redash查询的名称。
 * @param {function} [parameterAdjustments] 可选的参数调整函数，在发送前修改参数。
 * @returns {Promise<TItem[] | null>} 数据项数组或在失败时返回null。
 */
async function fetchDataWithRedashHelper<TItem, TParams extends Record<string, any>>(
  apiMethod: (params: any) => Promise<ApiResponse<RedashDataWrapper<TItem>>>,
  baseQueryParams: TParams,
  redashQueryName: string,
  parameterAdjustments?: (params: TParams) => Record<string, any>
): Promise<TItem[] | null> {
  let effectiveParams = { ...baseQueryParams };
  if (parameterAdjustments) {
    effectiveParams = parameterAdjustments(effectiveParams) as TParams;
  }

  const redashCompatibleParams = convertToRedashParams(effectiveParams, redashQueryName);

  // Clean up PageIndex and PageSize if they are not part of the actual query parameters needed by Redash,
  // but were part of the base TParams for other uses.
  // convertToRedashParams might already handle this, but explicit removal can be safer if TParams is broad.
  // delete redashCompatibleParams.parameters.PageIndex;
  // delete redashCompatibleParams.parameters.PageSize;

  try {
    const response = await apiMethod(redashCompatibleParams);
    if (response.Type !== 200) {
      ElMessage.error(response.Content || `${redashQueryName} data request failed.`);
      return null;
    }
    return response.Data.Data;
  } catch (error: any) {
    console.error(`Error fetching ${redashQueryName}:`, error);
    ElMessage.error(`请求 ${redashQueryName} 数据失败: ${error.message || "未知错误"}`);
    return null;
  }
}

/**
 * @description 获取并初始化页面底部图表数据 (方案执行付费、方案执行状态、患者诊断统计)。
 */
const fetchBottomChartsData = async () => {
  const baseParamsForBottomCharts = {
    OrgIds: topChartsQueryParams.value.OrgIds,
    Keyword: topChartsQueryParams.value.Keyword,
    DeptId: topChartsQueryParams.value.DeptIds,
    StartTimeDt: topChartsQueryParams.value.QueryStartDate,
    EndTimeDt: topChartsQueryParams.value.QueryEndDate,
    PageSize: 9999,
  };

  const [resPrescriptionExecStatistic, resPrescriptionSendStatistic, resPatientDiagnoseStatistics] =
    await Promise.all([
      fetchDataWithRedashHelper<PrescriptionExecStatisticItem, typeof baseParamsForBottomCharts>(
        reportApi.getRedashList,
        baseParamsForBottomCharts,
        "Report_PrescriptionExecStatistic",
        (params) => ({ ...params, ViewMode: bottomChartsViewMode.value })
      ),
      fetchDataWithRedashHelper<PrescriptionSendStatisticItem, typeof baseParamsForBottomCharts>(
        reportApi.getRedashList,
        baseParamsForBottomCharts,
        "Report_PrescriptionSendStatistic",
        (params) => ({ ...params, OrgView: topChartsQueryParams.value.OrgIds ? "0" : "1" })
      ),
      fetchDataWithRedashHelper<PatientDiagnoseStatisticsItem, typeof baseParamsForBottomCharts>(
        reportApi.getRedashList,
        baseParamsForBottomCharts, // No specific ViewMode or OrgView for this one
        "Report_PatientDiagnoseStatistics"
      ),
    ]);

  if (resPrescriptionExecStatistic) {
    initializePrescriptionExecutionFeeChart(resPrescriptionExecStatistic);
  }
  if (resPrescriptionSendStatistic) {
    initializePrescriptionExecutionStatusChart(resPrescriptionSendStatistic);
  }
  if (resPatientDiagnoseStatistics) {
    initializePatientDiagnosisStatsChart(resPatientDiagnoseStatistics);
  }
};

/**
 * @description 获取并初始化页面顶部图表数据 (患者统计、性别比例、年龄分布、疾病标签) 及累计患者总数。
 */
const fetchTopChartsDataAndSummary = async () => {
  const baseParamsForTopCharts = { ...topChartsQueryParams.value };

  const [patientStatsData, diseaseTagData] = await Promise.all([
    fetchDataWithRedashHelper<PatientStatisticsItem, TopChartsQueryParams>(
      reportApi.getRedashList,
      baseParamsForTopCharts,
      "Report_PatientStatistics",
      (params) => {
        const adjustedParams = { ...params };
        delete adjustedParams.QueryEndDate;
        delete adjustedParams.QueryStartDate;
        return adjustedParams;
      }
    ),
    fetchDataWithRedashHelper<PatientDiseaseTagStatisticsItem, TopChartsQueryParams>(
      reportApi.getRedashList,
      baseParamsForTopCharts,
      "Report_PatientDiseaseTagStatistics",
      (params) => {
        const adjustedParams = { ...params };
        delete adjustedParams.QueryStartDate;
        delete adjustedParams.QueryEndDate;
        return adjustedParams;
      }
    ),
  ]);

  if (patientStatsData && patientStatsData.length > 0) {
    totalPatientCount.value = Number(patientStatsData[0].PatientCount);
    initializePatientSexRatioChart(patientStatsData[0]);
    initializePatientAgeDistributionChart(patientStatsData[0]);
  }
  if (diseaseTagData) {
    initializePatientDiseaseStatsChart(diseaseTagData);
  }
};

/**
 * @description 初始化患者性别比例饼图。
 * @param {PatientStatisticsItem} info 患者统计信息，包含男女患者数量。
 */
const initializePatientSexRatioChart = (info: PatientStatisticsItem) => {
  try {
    sexChartInstance!.setOption({
      title: {
        text: "患者性别比例",
        left: "center",
      },
      tooltip: {
        trigger: "item",
      },
      series: [
        {
          type: "pie",
          radius: "60%",
          data: [
            { value: Number(info.ManPatientCount), name: "男" },
            { value: Number(info.WomanPatientCount), name: "女" },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    });
  } catch (error) {}
};
/**
 * @description 初始化患者年龄分布饼图。
 * @param {PatientStatisticsItem} data 患者统计信息，包含各年龄段数量及平均年龄。
 */
const initializePatientAgeDistributionChart = (data: PatientStatisticsItem) => {
  try {
    ageChartInstance!.setOption({
      title: {
        text:
          "患者年龄分布(平均年龄" +
          (data.AverageAge ? Number(data.AverageAge).toFixed(1) : "0") +
          "岁)",
        left: "center",
      },
      tooltip: {
        trigger: "item",
      },
      series: [
        {
          type: "pie",
          radius: "60%",
          data: [
            { value: Number(data.AgeLessThan20), name: "小于20岁" },
            { value: Number(data.AgeBetween20To40), name: "20-40岁" },
            { value: Number(data.AgeBetween40To60), name: "40-60岁" },
            { value: Number(data.AgeGreaterThan60), name: "60岁以上" },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    });
  } catch (error) {}
};
/**
 * @description 初始化患者疾病统计柱状图。
 * @param {PatientDiseaseTagStatisticsItem[]} data 患者疾病标签统计数据数组。
 */
const initializePatientDiseaseStatsChart = (data: PatientDiseaseTagStatisticsItem[]) => {
  try {
    diseaseChartInstance!.setOption({
      title: {
        text: "患者疾病统计",
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: {
        type: "value",
        boundaryGap: [0, 0.01],
      },
      yAxis: {
        type: "category",
        data: data.map((v) => v.Tag),
      },
      series: [
        {
          name: "人数",
          type: "bar",
          data: data.map((v) => v.Count),
        },
      ],
    });
  } catch (error) {}
};
/**
 * @description 初始化方案执行情况（付费）折线图。
 * @param {PrescriptionExecStatisticItem[]} data 方案执行统计数据数组 (付费部分)。
 */
const initializePrescriptionExecutionFeeChart = (data: PrescriptionExecStatisticItem[]) => {
  const xData = data.map((v) => v.Date);
  try {
    prescriptionExecutionFeeChartInstance!.setOption({
      title: {
        text: "方案执行情况（付费）",
      },
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: ["下达方案数", "已执行方案数"],
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {},
        },
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: xData,
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          name: "下达方案数",
          type: "line",
          data: data.map((v) => v.TotalCnt),
        },
        {
          name: "已执行方案数",
          type: "line",
          data: data.map((v) => v.ExecTotalCnt),
        },
      ],
      dataZoom: [
        {
          type: "inside",
          xAxisIndex: 0,
          start: 0,
          end: 100,
        },
      ],
    });
  } catch (error) {}
};
/**
 * @description 初始化方案执行情况柱状图。
 * @param {PrescriptionSendStatisticItem[]} data 方案下达与执行统计数据数组。
 */
const initializePrescriptionExecutionStatusChart = (data: PrescriptionSendStatisticItem[]) => {
  try {
    prescriptionExecutionStatusChartInstance!.setOption({
      title: {
        text: "方案执行情况",
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
      },
      legend: {
        data: ["下达方案数", "已执行方案数"],
      },
      toolbox: {
        show: true,
        orient: "vertical",
        left: "right",
        top: "center",
        feature: {
          mark: { show: true },
          dataView: { show: true, readOnly: false },
          magicType: { show: true, type: ["line", "bar", "stack"] },
          restore: { show: true },
          saveAsImage: { show: true },
        },
      },
      xAxis: [
        {
          type: "category",
          data: data.map((v) => v.Name),
          axisLabel: {
            interval: 0,
            rotate: 20,
          },
        },
      ],
      yAxis: [
        {
          type: "value",
        },
      ],
      series: [
        {
          name: "下达方案数",
          type: "bar",
          barGap: 0,
          emphasis: {
            focus: "series",
          },

          data: data.map((v) => v.TotalCnt),
        },
        {
          name: "已执行方案数",
          type: "bar",
          emphasis: {
            focus: "series",
          },

          data: data.map((v) => v.ExecTotalCnt),
        },
      ],
      dataZoom: [
        {
          type: "inside",
          xAxisIndex: 0,
          start: 0,
          end: 100,
        },
      ],
    });
  } catch (error) {}
};
/**
 * @description 初始化患者诊断统计柱状图。
 * @param {PatientDiagnoseStatisticsItem[]} data 患者诊断统计数据数组。
 */
const initializePatientDiagnosisStatsChart = (data: PatientDiagnoseStatisticsItem[]) => {
  try {
    patientDiagnosisStatsChartInstance!.setOption({
      title: {
        text: "诊断",
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
      },
      toolbox: {
        show: true,
        orient: "vertical",
        left: "right",
        top: "center",
        feature: {
          mark: { show: true },
          dataView: { show: true, readOnly: false },
          magicType: { show: true, type: ["line", "bar", "stack"] },
          restore: { show: true },
          saveAsImage: { show: true },
        },
      },
      xAxis: {
        type: "category",
        data: data.map((v) => v.DiagnoseName),
        axisLabel: {
          interval: 0,
          rotate: 20,
        },
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          data: data.map((v) => v.Count),
          type: "bar",
        },
      ],
    });
  } catch (error) {}
};

/**
 * @description 加载页面所有图表数据。会触发isLoading状态。
 */
const loadPageData = async () => {
  isLoading.value = true;
  try {
    await Promise.all([fetchTopChartsDataAndSummary(), fetchBottomChartsData()]);
  } catch (error) {
    console.error("Error loading page data:", error);
    ElMessage.error("页面数据加载失败");
  } finally {
    isLoading.value = false;
  }
};

watch(bottomChartsTimeRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    topChartsQueryParams.value.QueryStartDate = dayjs(newVal[0]).format("YYYY-MM-01 00:00:00");
    topChartsQueryParams.value.QueryEndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
  } else {
    topChartsQueryParams.value.QueryStartDate = dayjs().format("YYYY-MM-01 00:00:00");
    topChartsQueryParams.value.QueryEndDate = dayjs().format("YYYY-MM-DD 23:59:59");
  }
});

let allChartInstances: EChartsType[] = [];

/**
 * @description 初始化页面上所有ECharts图表实例。
 * 会先销毁已存在的实例，然后根据当前主题重新初始化，并添加到 allChartInstances 统一管理。
 * 同时会添加窗口resize事件监听器以自适应图表大小。
 */
const initCharts = () => {
  allChartInstances.forEach((chart) => {
    // Dispose existing before re-init, just in case
    if (chart && !chart.isDisposed()) {
      chart.dispose();
    }
  });
  allChartInstances = []; // Reset the array

  // Helper to initialize and add to list
  const initAndTrack = (
    instanceVar: EChartsType | undefined,
    refVar: Ref<HTMLDivElement | undefined>
  ): EChartsType | undefined => {
    let newInstance = initSingleChart(instanceVar, refVar);
    if (newInstance) {
      allChartInstances.push(newInstance);
    }
    return newInstance;
  };

  diseaseChartInstance = initAndTrack(diseaseChartInstance, diseaseChartRef);
  sexChartInstance = initAndTrack(sexChartInstance, sexChartRef);
  ageChartInstance = initAndTrack(ageChartInstance, ageChartRef);
  prescriptionExecutionFeeChartInstance = initAndTrack(
    prescriptionExecutionFeeChartInstance,
    prescriptionExecutionFeeChartRef
  );
  prescriptionExecutionStatusChartInstance = initAndTrack(
    prescriptionExecutionStatusChartInstance,
    prescriptionExecutionStatusChartRef
  );
  patientDiagnosisStatsChartInstance = initAndTrack(
    patientDiagnosisStatsChartInstance,
    patientDiagnosisStatsChartRef
  );

  window.addEventListener("resize", handleResizeCharts);
};

const initSingleChart = (
  chartInstance: EChartsType | undefined,
  containerRef: Ref<HTMLDivElement | undefined>
) => {
  if (containerRef.value) {
    const existingInstance = echarts.getInstanceByDom(containerRef.value);
    if (existingInstance) {
      existingInstance.dispose();
    }
    if (containerRef.value.offsetWidth > 0 && containerRef.value.offsetHeight > 0) {
      try {
        const themeToUse = getCurrentChartThemeName() === "dark" ? darkTheme : lightTheme;
        return echarts.init(containerRef.value, undefined, { renderer: "svg", ...themeToUse });
      } catch (error) {
        console.error(`Failed to initialize chart:`, error);
      }
    }
  }
  return undefined;
};

/**
 * @description 销毁所有已初始化的ECharts图表实例并移除resize监听器。
 * 通常在组件失活时调用以释放资源。
 */
const disposeCharts = () => {
  allChartInstances.forEach((chart) => {
    if (chart && !chart.isDisposed()) {
      chart.dispose();
    }
  });
  allChartInstances = [];
  window.removeEventListener("resize", handleResizeCharts);
};

/**
 * @description 处理窗口大小变化事件，自动调整所有图表尺寸。
 */
const handleResizeCharts = () => {
  allChartInstances.forEach((chart) => {
    if (chart && !chart.isDisposed()) {
      try {
        chart.resize();
      } catch (e) {
        console.warn("Error resizing chart:", e);
      }
    }
  });
};

/**
 * Vue lifecycle hook: 组件被激活时调用。
 * 会初始化图表并加载页面数据。
 */
onActivated(async () => {
  await nextTick();
  initCharts(); // This will also re-apply themes if mode changed while inactive
  loadPageData();
});

/**
 * Vue lifecycle hook: 组件失活时调用。
 * 会销毁所有图表以释放资源。
 */
onDeactivated(() => {
  disposeCharts();
});
</script>

<style lang="scss" scoped></style>
