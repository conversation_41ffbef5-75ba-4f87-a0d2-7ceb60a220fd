<template>
  <div class="left-root">
    <div class="left-root-bgImg">
      <div class="left-root-bgImg-bg">
        <span class="left-root-bgImg-bg-title">医院收入</span>

        <div class="left-root-bgImg-bg-time">
          <el-date-picker
            v-model="value2"
            type="daterange"
            :teleported="false"
            :editable="false"
            :shortcuts="datePickerShortcuts"
            start-placeholder="开始日期"
            range-separator="至"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="onChangeTime"
          />
        </div>
      </div>
    </div>
    <div class="left-root-chart">
      <div v-for="(i, index) in orgList" :key="index" class="left-root-chart-box">
        <div class="left-root-chart-box-top">
          <span>{{ i.Name }}</span>
          <span :style="{ color: colorList[index] }">{{ i.Amount }}</span>
        </div>
        <div class="left-root-chart-box-progress">
          <el-progress
            :percentage="Number(i.Percentage ?? 0)"
            :show-text="false"
            :stroke-width="10"
            :color="colorList[index]"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import dayjs from "dayjs";
import type { OrgEntity } from "../index.vue";

export default defineComponent({
  props: {
    list: {
      type: Array as PropType<OrgEntity[]>,
      default: () => [],
    },
  },
  data() {
    return {
      value2: [dayjs().format("YYYY-MM-01 00:00:00"), dayjs().format("YYYY-MM-DD  23:59:59")],
      datePickerShortcuts: [
        {
          text: "本月",
          value() {
            const end = dayjs().format("YYYY-MM-DD 00:00:00");
            const start = dayjs().format("YYYY-MM-01 00:00:00");
            return [start, end];
          },
        },
        {
          text: "本年",
          value() {
            const end = dayjs().format("YYYY-MM-DD 00:00:00");
            const start = dayjs().startOf("year").format("YYYY-MM-DD 00:00:00");
            return [start, end];
          },
        },
        {
          text: "近一个月",
          value() {
            const end = dayjs().format("YYYY-MM-DD 00:00:00");
            const start = dayjs().subtract(1, "months").format("YYYY-MM-DD 00:00:00");
            return [start, end];
          },
        },
        {
          text: "近三个月",
          value() {
            const end = dayjs().format("YYYY-MM-DD 00:00:00");
            const start = dayjs().subtract(3, "months").format("YYYY-MM-DD 00:00:00");
            return [start, end];
          },
        },
        {
          text: "近半年",
          value() {
            const end = dayjs().format("YYYY-MM-DD 00:00:00");
            const start = dayjs().subtract(6, "months").format("YYYY-MM-DD 00:00:00");
            return [start, end];
          },
        },
        {
          text: "近一年",
          value() {
            const end = dayjs().format("YYYY-MM-DD 00:00:00");
            const start = dayjs().subtract(12, "months").format("YYYY-MM-DD 00:00:00");
            return [start, end];
          },
        },
      ],
      orgList: [] as OrgEntity[],
      colorList: [
        "#29F1F5",
        "#29D6F5",
        "#29CCF5",
        "#29A0F5",
        "#2285CC",
        "#1E75B3",
        "#1A6499",
        "#155380",
        "#114366",
        "#0D324D",
      ],
    };
  },
  watch: {
    list: {
      handler(newVal) {
        this.changeList(newVal);
      },
      deep: true,
    },
  },
  methods: {
    changeList(list: OrgEntity[]) {
      console.log("list", list);
      if (list.length <= 10) {
        this.orgList = list;
      } else {
        this.orgList = list.slice(0, 10);
      }
    },
    onChangeTime(e: any) {
      console.log("e", e);
      this.$emit("changeTime", {
        BeginTimeDt: dayjs(this.value2[0]).format("YYYY-MM-DD 00:00:00"),
        EndTimeDt: dayjs(this.value2[1]).format("YYYY-MM-DD 23:59:59"),
      });
    },
  },
});
</script>
<style scoped lang="scss">
:deep(.el-date-editor) {
  box-shadow: none !important;
  width: 280px !important;
  background: none !important;
  border: none;
}
:deep(.el-input__icon) {
  display: none !important;
}
:deep(.el-range-input) {
  background: #0a152a !important;
  font-family: YouSheBiaoTiHei !important;
  font-size: 16px !important;
  background: linear-gradient(180deg, #ffffff 0%, #54b8fe 100%) !important;
  background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  font-family: "YouSheBiaoTiHei" !important;
}
:deep(.el-range-separator) {
  color: white !important;
}
.left-root {
  width: 100%;
  height: 100%;
  &-chart {
    width: 100%;
    height: 94%;
    &-box {
      height: 8%;
      margin-top: 10px;
      padding-left: 20px;
      &-top {
        width: 100%;
        height: 50%;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      &-progress {
        width: 100%;
        height: 50%;
        margin-top: 2%;
        /* 当前状态颜色 */
        :deep(.el-progress-bar__outer) {
          background-color: #00000000;
        }
      }
    }
  }
  &-bgImg {
    width: 100%;
    height: 6%;
    background-image: url("../assets/bgimg.png");
    background-size: 100% 100%;
    position: relative;
    &-bg {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 85%;
      position: absolute;
      right: 0;
      top: 0;
      bottom: 30%;
      &-title {
        background: linear-gradient(180deg, #ffffff 0%, #54b8fe 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 18px;
        font-family: YouSheBiaoTiHei;
        color: #1ad7ff;
        font-family: "YouSheBiaoTiHei";
      }
    }
    &-list {
      position: absolute;
      top: 70%;
      z-index: 99;
      background: rgba(10, 76, 139, 1);
      right: 0;
      padding: 10px 10px 0 10px;
      &-each {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
