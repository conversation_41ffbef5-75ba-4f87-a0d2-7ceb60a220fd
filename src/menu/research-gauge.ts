import { type RouteVO } from "@/api/system/menu";

const researchGaugeRoutes: RouteVO[] = [
  {
    path: "/research-gauge-management",
    component: "Layout",
    name: "ResearchGaugeManagement",
    meta: {
      title: "科研量表管理",
      icon: "el-icon-collection",
      hidden: true,
      alwaysShow: false,
      roles: ["superOperate"],
    },
    children: [
      {
        path: "researchGauge",
        component: "research-gauge/research-gauge/index",
        name: "ResearchGauge",
        meta: {
          title: "科研量表管理",
          icon: "el-icon-tickets",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
    ],
  },
];
export default researchGaugeRoutes;
